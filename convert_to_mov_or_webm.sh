#!/bin/bash

# PNG序列转MOV视频脚本 (高质量透明版)
# 支持PNG文件在当前目录或子目录中
# 保持透明度和高质量，适用于web网页

# 检查ffmpeg是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo "错误: 未安装ffmpeg。请先安装ffmpeg。"
    echo "安装命令: brew install ffmpeg (macOS)"
    exit 1
fi

# 创建输出目录
output_dir="output_videos"
mkdir -p "$output_dir"

# 编码选项
echo "请选择编码方式:"
echo "1. ProRes 4444 (高质量，支持透明度，文件较大，兼容性好)"
echo "2. PNG编码 (最高质量，支持透明度，文件很大)"
echo "3. WebM VP9 (现代浏览器，支持透明度，文件适中)"
echo "4. H.264高质量 (无透明度，兼容性最好，文件小)"
read -p "请输入选择 (1-4，默认1): " choice
choice=${choice:-1}

# 设置基础参数
FRAMERATE=24          # 帧率 - 与参考文件保持一致
SCALE=""             # 保持原始分辨率，避免缩放造成的质量损失

# 根据选择设置编码参数
case $choice in
    1)
        CODEC="prores_ks"
        PROFILE="-profile:v 4444"
        QUALITY="-qscale:v 1"  # 最高质量
        EXT="mov"
        EXTRA_PARAMS="-pix_fmt yuva444p10le"
        echo "使用 ProRes 4444 编码 (支持透明度)"
        ;;
    2)
        CODEC="png"
        PROFILE=""
        QUALITY=""
        EXT="mov"
        EXTRA_PARAMS="-pix_fmt rgba"
        echo "使用 PNG 编码 (最高质量，支持透明度)"
        ;;
    3)
        CODEC="libvpx-vp9"
        PROFILE=""
        QUALITY="-crf 20 -b:v 2000k -minrate 1000k -maxrate 3000k"
        EXT="webm"
        EXTRA_PARAMS="-pix_fmt yuva420p -auto-alt-ref 1 -lag-in-frames 25 -g 240 -row-mt 1"
        echo "使用 VP9 编码 (支持透明度，优化web播放)"
        ;;
    4)
        CODEC="libx264"
        PROFILE=""
        QUALITY="-crf 15 -preset slow"
        EXT="mov"
        EXTRA_PARAMS="-pix_fmt yuv420p"
        echo "使用 H.264 高质量编码 (无透明度)"
        ;;
    *)
        echo "无效选择，使用默认 ProRes 4444"
        CODEC="prores_ks"
        PROFILE="-profile:v 4444"
        QUALITY="-qscale:v 1"
        EXT="mov"
        EXTRA_PARAMS="-pix_fmt yuva444p10le"
        ;;
esac

echo ""
echo "开始转换PNG序列为视频..."
echo "参数设置:"
echo "- 帧率: ${FRAMERATE}fps"
echo "- 编码器: ${CODEC}"
echo "- 输出格式: ${EXT}"
echo "- 保持原始分辨率: 是"
echo "================================"

# 函数：处理PNG序列
process_png_sequence() {
    local search_dir="$1"
    local output_name="$2"
    
    # 查找PNG文件并按数字排序
    png_files=($(find "$search_dir" -maxdepth 1 -name "*.png" | sort))
    png_count=${#png_files[@]}
    
    if [ $png_count -eq 0 ]; then
        return 1
    fi
    
    echo "处理目录: $search_dir (包含 $png_count 个PNG文件)"
    
    # 检测命名模式
    first_png="${png_files[0]}"
    base_name=$(basename "$first_png")
    
    # 尝试不同的命名模式
    if [[ $base_name =~ ^(.+)_([0-9]{5})\.png$ ]]; then
        # 格式: name_00000.png
        prefix="${BASH_REMATCH[1]}"
        input_pattern="$search_dir/${prefix}_%05d.png"
        echo "  检测到命名模式: ${prefix}_#####.png"
    elif [[ $base_name =~ ^(.+)_([0-9]+)\.png$ ]]; then
        # 格式: name_0.png, name_1.png 等
        prefix="${BASH_REMATCH[1]}"
        # 检测数字位数
        digit_count=${#BASH_REMATCH[2]}
        input_pattern="$search_dir/${prefix}_%0${digit_count}d.png"
        echo "  检测到命名模式: ${prefix}_数字.png"
    elif [[ $base_name =~ ^([0-9]{5})\.png$ ]]; then
        # 格式: 00000.png
        input_pattern="$search_dir/%05d.png"
        echo "  检测到命名模式: #####.png"
    elif [[ $base_name =~ ^([0-9]+)\.png$ ]]; then
        # 格式: 0.png, 1.png 等
        digit_count=${#BASH_REMATCH[1]}
        input_pattern="$search_dir/%0${digit_count}d.png"
        echo "  检测到命名模式: 数字.png"
    else
        echo "  ⚠️  警告: 无法识别命名模式，使用通配符模式"
        # 创建临时符号链接以确保顺序
        temp_dir=$(mktemp -d)
        counter=0
        for png_file in "${png_files[@]}"; do
            ln -s "$(realpath "$png_file")" "$temp_dir/$(printf "%05d.png" $counter)"
            ((counter++))
        done
        input_pattern="$temp_dir/%05d.png"
    fi
    
    output_file="$output_dir/${output_name}.${EXT}"
    echo "  输入模式: $input_pattern"
    echo "  输出文件: $output_file"
    
    # 构建ffmpeg命令
    ffmpeg_cmd="ffmpeg -y -framerate $FRAMERATE -i \"$input_pattern\" -c:v $CODEC"
    
    if [ -n "$PROFILE" ]; then
        ffmpeg_cmd="$ffmpeg_cmd $PROFILE"
    fi
    
    if [ -n "$QUALITY" ]; then
        ffmpeg_cmd="$ffmpeg_cmd $QUALITY"
    fi
    
    if [ -n "$SCALE" ]; then
        ffmpeg_cmd="$ffmpeg_cmd -vf \"scale=$SCALE:flags=lanczos\""
    fi
    
    ffmpeg_cmd="$ffmpeg_cmd $EXTRA_PARAMS"
    
    # 添加特定格式的优化
    if [ "$EXT" = "mov" ]; then
        ffmpeg_cmd="$ffmpeg_cmd -movflags +faststart"
    elif [ "$EXT" = "webm" ]; then
        ffmpeg_cmd="$ffmpeg_cmd -deadline good -cpu-used 1 -tile-columns 2 -tile-rows 1 -frame-parallel 1 -threads 8"
    fi
    
    ffmpeg_cmd="$ffmpeg_cmd \"$output_file\""
    
    echo "  执行命令: $ffmpeg_cmd"
    
    # 执行ffmpeg转换
    eval $ffmpeg_cmd 2>/dev/null
    
    # 清理临时目录
    if [[ -n "$temp_dir" && -d "$temp_dir" ]]; then
        rm -rf "$temp_dir"
    fi
    
    if [ $? -eq 0 ]; then
        # 获取文件大小
        file_size=$(du -h "$output_file" | cut -f1)
        echo "  ✅ 成功: $output_file (大小: $file_size)"
        return 0
    else
        echo "  ❌ 失败: $output_file"
        return 1
    fi
}

# 处理计数器
processed_count=0

# 1. 首先检查当前目录是否有PNG序列
echo "检查当前目录..."
if process_png_sequence "." "current_directory"; then
    ((processed_count++))
    echo ""
fi

# 2. 然后处理所有子目录
echo "检查子目录..."
for dir in */; do
    # 移除目录名末尾的斜杠
    dir_name=${dir%/}
    
    # 跳过输出目录
    if [ "$dir_name" = "$output_dir" ]; then
        continue
    fi
    
    if process_png_sequence "$dir" "$dir_name"; then
        ((processed_count++))
    fi
    echo ""
done

echo "================================"
echo "转换完成！处理了 $processed_count 个PNG序列"
echo "输出目录: $output_dir"

if [ $processed_count -gt 0 ]; then
    echo "生成的视频文件:"
    ls -lh "$output_dir"/*.$EXT 2>/dev/null
else
    echo "未找到任何PNG序列文件"
fi

echo ""
echo "💡 提示:"
echo "- ProRes 4444: 专业级质量，支持透明度，适合后期处理"
echo "- PNG编码: 无损质量，支持透明度，文件最大"
echo "- VP9编码: 现代web标准，支持透明度，参考lottery.webm优化"
echo "- H.264: 最广泛兼容，但不支持透明度"
echo "- 所有编码都保持原始分辨率和高质量设置"