// 弹窗配置管理系统

// 弹窗配置接口
export interface PopupConfig {
  id: string; // 弹窗唯一标识
  name: string; // 弹窗名称
  delay: number; // 延迟出现时间（秒）
  closable: boolean; // 是否允许关闭
  closeButtonDelay: number; // 关闭按钮延迟出现时间（秒）
  eventName: string; // 弹窗上报事件名
  priority: number; // 优先级（数字越小优先级越高）
  conditions?: {
    maxShowCount?: number; // 最大展示次数
    cooldown?: number; // 冷却时间（秒）
    pageRestriction?: string[]; // 页面限制
  };
}

// 弹窗配置组
export interface PopupGroupConfig {
  groupId: string; // 配置组ID
  globalEventName: string; // 全局弹窗上报事件名
  popups: PopupConfig[]; // 弹窗列表
  strategy: 'sequential' | 'random' | 'priority'; // 弹窗展示策略
  globalCooldown: number; // 全局冷却时间（秒）
}

// 弹窗状态接口
interface PopupState {
  hasReported: boolean; // 是否已上报
  showCount: number; // 展示次数
  lastShowTime: number; // 最后展示时间
  lastReportTime: number; // 最后上报时间
}

// 全局状态接口
interface GlobalPopupState {
  hasReportedGlobal: boolean; // 是否已上报全局事件
  lastGlobalReportTime: number; // 最后全局上报时间
  lastPopupTime: number; // 最后弹窗时间
}

// 默认弹窗配置
export const defaultPopupConfig: PopupGroupConfig = {
  groupId: 'order_popups',
  globalEventName: 'imp_popup_all',
  strategy: 'random',
  globalCooldown: 5,
  popups: [
    {
      id: 'popup_a',
      name: '预约弹窗A',
      delay: 3,
      closable: true,
      closeButtonDelay: 3,
      eventName: 'imp_popup1',
      priority: 1,
      conditions: {
        maxShowCount: 10,
        cooldown: 300,
        pageRestriction: ['pre', 'pre2']
      }
    },
    {
      id: 'popup_c',
      name: '预约弹窗C',
      delay: 3,
      closable: true,
      closeButtonDelay: 3,
      eventName: 'imp_popup3',
      priority: 2,
      conditions: {
        maxShowCount: 10,
        cooldown: 300,
        pageRestriction: ['pre', 'pre2']
      }
    }
  ]
};

// 弹窗管理类
export class PopupManager {
  private config: PopupGroupConfig;
  private storageKey: string;

  constructor(config: PopupGroupConfig = defaultPopupConfig) {
    this.config = config;
    this.storageKey = `popup_state_${config.groupId}`;
  }

  // 获取弹窗状态
  private getPopupState(popupId: string): PopupState {
    const states = this.getAllStates();
    return states[popupId] || {
      hasReported: false,
      showCount: 0,
      lastShowTime: 0,
      lastReportTime: 0
    };
  }

  // 获取全局状态
  private getGlobalState(): GlobalPopupState {
    const states = this.getAllStates();
    return states._global || {
      hasReportedGlobal: false,
      lastGlobalReportTime: 0,
      lastPopupTime: 0
    };
  }

  // 获取所有状态
  private getAllStates(): any {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  // 保存状态
  private saveState(popupId: string, state: PopupState | GlobalPopupState): void {
    try {
      const states = this.getAllStates();
      states[popupId] = state;
      localStorage.setItem(this.storageKey, JSON.stringify(states));
    } catch (error) {
      console.warn('Failed to save popup state:', error);
    }
  }

  // 检查弹窗是否可以展示
  canShowPopup(popupId: string, currentPage?: string): boolean {
    const popup = this.config.popups.find(p => p.id === popupId);
    if (!popup) return false;

    const state = this.getPopupState(popupId);
    const globalState = this.getGlobalState();
    const now = Date.now();

    // 检查页面限制
    if (popup.conditions?.pageRestriction && currentPage) {
      if (!popup.conditions.pageRestriction.includes(currentPage)) {
        return false;
      }
    }

    // 检查最大展示次数
    if (popup.conditions?.maxShowCount && state.showCount >= popup.conditions.maxShowCount) {
      return false;
    }

    // 检查冷却时间
    if (popup.conditions?.cooldown) {
      const timeSinceLastShow = (now - state.lastShowTime) / 1000;
      if (timeSinceLastShow < popup.conditions.cooldown) {
        return false;
      }
    }

    // 检查全局冷却时间
    const timeSinceLastGlobal = (now - globalState.lastPopupTime) / 1000;
    if (timeSinceLastGlobal < this.config.globalCooldown) {
      return false;
    }

    return true;
  }

  // 获取下一个要展示的弹窗
  getNextPopup(currentPage?: string): PopupConfig | null {
    const availablePopups = this.config.popups.filter(popup => 
      this.canShowPopup(popup.id, currentPage)
    );

    if (availablePopups.length === 0) return null;

    switch (this.config.strategy) {
      case 'priority':
        return availablePopups.sort((a, b) => a.priority - b.priority)[0];
      case 'random':
        return availablePopups[Math.floor(Math.random() * availablePopups.length)];
      case 'sequential':
        return availablePopups[0];
      default:
        return availablePopups[0];
    }
  }

  // 记录弹窗展示
  recordPopupShow(popupId: string): void {
    const state = this.getPopupState(popupId);
    const globalState = this.getGlobalState();
    const now = Date.now();

    // 更新弹窗状态
    this.saveState(popupId, {
      ...state,
      showCount: state.showCount + 1,
      lastShowTime: now
    });

    // 更新全局状态
    this.saveState('_global', {
      ...globalState,
      lastPopupTime: now
    });
  }

  // 检查是否需要上报事件
  shouldReportEvent(popupId: string): boolean {
    const state = this.getPopupState(popupId);
    return !state.hasReported;
  }

  // 检查是否需要上报全局事件
  shouldReportGlobalEvent(): boolean {
    const globalState = this.getGlobalState();
    return !globalState.hasReportedGlobal;
  }

  // 记录事件上报
  recordEventReport(popupId: string): void {
    const state = this.getPopupState(popupId);
    this.saveState(popupId, {
      ...state,
      hasReported: true,
      lastReportTime: Date.now()
    });
  }

  // 记录全局事件上报
  recordGlobalEventReport(): void {
    const globalState = this.getGlobalState();
    this.saveState('_global', {
      ...globalState,
      hasReportedGlobal: true,
      lastGlobalReportTime: Date.now()
    });
  }

  // 重置状态（用于测试或特殊情况）
  resetState(popupId?: string): void {
    if (popupId) {
      this.saveState(popupId, {
        hasReported: false,
        showCount: 0,
        lastShowTime: 0,
        lastReportTime: 0
      });
    } else {
      localStorage.removeItem(this.storageKey);
    }
  }

  // 获取弹窗配置
  getPopupConfig(popupId: string): PopupConfig | undefined {
    return this.config.popups.find(p => p.id === popupId);
  }

  // 更新配置
  updateConfig(config: PopupGroupConfig): void {
    this.config = config;
    this.storageKey = `popup_state_${config.groupId}`;
  }

  // 获取配置
  getConfig(): PopupGroupConfig {
    return this.config;
  }
}

// 导出默认实例
export const popupManager = new PopupManager();

// 工具函数：延迟执行
export const delay = (seconds: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
};

// 工具函数：创建弹窗控制器
export const createPopupController = (config?: PopupGroupConfig) => {
  const manager = new PopupManager(config);
  
  return {
    // 显示弹窗
    async showPopup(currentPage?: string, onShow?: (popup: PopupConfig) => void, onReport?: (eventName: string) => void) {
      const popup = manager.getNextPopup(currentPage);
      if (!popup) return null;

      // 延迟显示
      await delay(popup.delay);

      // 记录展示
      manager.recordPopupShow(popup.id);

      // 上报全局事件
       if (manager.shouldReportGlobalEvent()) {
         onReport?.(manager.getConfig().globalEventName);
         manager.recordGlobalEventReport();
       }

      // 上报弹窗事件
      if (manager.shouldReportEvent(popup.id)) {
        onReport?.(popup.eventName);
        manager.recordEventReport(popup.id);
      }

      // 执行显示回调
      onShow?.(popup);

      return popup;
    },

    // 获取管理器实例
    getManager: () => manager
  };
};
