class DateUtils {
  // 私有构造函数防止实例化
  private constructor() {}

  // 静态方法：格式化时间
  static formatDate(isoString: string): string {
    const date = new Date(isoString);
    return `${date.getFullYear()}/${String(date.getMonth() + 1).padStart(2, "0")}/${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`;
  }

  // 其他静态方法可以继续添加，比如解析日期、获取时间差等
  static parseDate(dateString: string): Date {
    return new Date(dateString);
  }

  // 计算倒计时数据
  static calculateCountdown = (endTime: number, currentTime: number) => {
    const sevenDaysInSeconds = 7 * 24 * 60 * 60; // 7天的秒数

    if (endTime > currentTime && endTime - currentTime <= sevenDaysInSeconds) {
      const remainingSeconds = endTime - currentTime;

      const days = String(Math.floor(remainingSeconds / (24 * 60 * 60))).padStart(2, '0');
      const hours = String(Math.floor((remainingSeconds % (24 * 60 * 60)) / (60 * 60))).padStart(2, '0');
      const minutes = String(Math.floor((remainingSeconds % (60 * 60)) / 60)).padStart(2, '0');
      const seconds = String(remainingSeconds % 60).padStart(2, '0');
      return {
        days,
        hours,
        minutes,
        seconds,
      };
    }
    return undefined;
  };
}

export default DateUtils;
