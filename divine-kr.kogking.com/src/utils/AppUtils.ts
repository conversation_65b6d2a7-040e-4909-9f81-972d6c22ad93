import md5 from "md5";

class AppUtils {
  private constructor() {}

  static copy(value: string, cb?: any) {
    const textarea = document.createElement("textarea");
    textarea.readOnly = true;
    textarea.style.position = "absolute";
    textarea.style.left = "-9999px";
    textarea.value = value;
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand("Copy");
    document.body.removeChild(textarea);
    if (cb && Object.prototype.toString.call(cb) === "[object Function]") {
      cb();
    }
  }

  static getDeviceType(): "Android" | "iOS" | "PC" {
    let deviceType: "Android" | "iOS" | "PC" = "PC";
    const userAgent: string = navigator.userAgent || navigator.vendor || (window as any).opera;

    // 判断 Android
    if (/android/i.test(userAgent)) {
      deviceType = "Android";
    }
    // 判断 iOS
    else if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream) {
      deviceType = "iOS";
    }

    return deviceType;
  }

  static isIOS() {
    return /(iphone|ipad|ipod)/i.test(navigator.userAgent);
  }

  static getBrowserLangCode() {
    // 定义匹配规则（优先级从高到低）
    const langMap = [
      { test: (lang: string) => /^zh/i.test(lang), code: "TW" }, // 繁体中文（台湾）
      { test: (lang: string) => /^ko/i.test(lang), code: "KR" }, // 韩语
      { test: (lang: string) => /^vi/i.test(lang), code: "VN" }, // 越南语
      { test: (lang: string) => /^en/i.test(lang), code: "EN" }, // 英语
    ];

    // 遍历浏览器语言列表进行匹配
    for (const rule of langMap) {
      if (rule.test(navigator.language.trim())) {
        return rule.code;
      }
    }

    return "EN"; // 默认返回英语
  }

  static generateSign = (params: any, secretkey: string = "9f6b066e102853f891bc6e93e5ca76f7") => {
    //1.移除sign字段(如果存在)
    const paramsWithoutSign = { ...params };
    delete paramsWithoutSign.sign;

    //2.按ASCII码排序字段
    const sortedKeys = Object.keys(paramsWithoutSign).sort();

    //3.拼接成URL格式
    const urlString = sortedKeys.map((key) => `${key}=${encodeURIComponent(paramsWithoutSign[key])}`).join("&");

    //4.拼接url参数和密钥
    const stringToSign = `${secretkey}${urlString}`;

    //5.MD5加密
    return md5(stringToSign);
  };

  static shareFb = (code: string, fbAppId: string) => {
    const href = `https://divine-kr.kogking.com/reserve?code=${code}`;
    let shareUrl = `http://www.facebook.com/sharer/sharer.php?u=${href}`;
    // let shareUrl = `https://www.facebook.com/dialog/share?app_id=${fbAppId}&display=popup&href=${href}`;

    let w = 375;
    let h = 400;
    let wLeft = window.screenLeft ? window.screenLeft : window.screenX;
    let wTop = window.screenTop ? window.screenTop : window.screenY;
    let left = wLeft + window.innerWidth / 2 - w / 2;
    let top = wTop + window.innerHeight / 2 - h / 2;
    return window.open(
      shareUrl,
      "facebook",
      "toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, resizable=no, copyhistory=no, width=" + w + ", height=" + h + ", top=" + top + ", left=" + left
    );
  };

  static getChinaTime() {
    const now = new Date();
    // 转换为中国时区 (UTC+8)
    now.setHours(now.getHours() + now.getTimezoneOffset() / 60 + 8);

    // 格式化为 YYYY-MM-DD
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  }
}

export default AppUtils;
