import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory("/"),
  routes: [
    {
      path: "/",
      component: () => import("../views/HomeView.vue"),
    },
    {
      path: "/reserve/:recommend_utm?",
      name: "reserve",
      component: () => import("../views/HomeView.vue"),
    },
    {
      path: "/shorts1/:recommend_utm?",
      name: "shorts1",
      component: () => import("../views/shorts/index.vue"),
    },
    {
      path: "/shorts2/:recommend_utm?",
      name: "shorts2",
      component: () => import("../views/shorts/index.vue"),
    },
    {
      path: "/shorts3/:recommend_utm?",
      name: "shorts3",
      component: () => import("../views/shorts/index.vue"),
    },
  ],
});

export default router;
