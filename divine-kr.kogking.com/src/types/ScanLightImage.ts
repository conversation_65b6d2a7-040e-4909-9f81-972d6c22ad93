/**
 * 扫光图片组件相关类型定义
 */

/** 扫光方向枚举 */
export type ScanDirection = "left-to-right" | "right-to-left" | "top-to-bottom" | "bottom-to-top";

/** 扫光图片组件Props接口 */
export interface ScanLightImageProps {
  /** 图片URL（可选，当使用slot时不需要） */
  src?: string;
  /** 图片alt属性（可选） */
  alt?: string;
  /** 扫光方向（可选，默认从左到右） */
  scanDirection?: ScanDirection;
  /** 扫光动画持续时间（可选，默认2秒） */
  scanDuration?: number;
  /** 是否自动播放扫光动画（可选，默认false） */
  autoScan?: boolean;
  /** 自动扫光间隔时间（可选，默认5秒） */
  autoScanInterval?: number;
  /** 呼吸动画 */
  breathe?: boolean;
}

/** 扫光图片组件暴露的方法接口 */
export interface ScanLightImageExpose {
  /** 手动触发扫光动画 */
  triggerScan: () => void;
}

/** 扫光动画配置接口 */
export interface ScanAnimationConfig {
  /** 动画持续时间（秒） */
  duration: number;
  /** 动画方向 */
  direction: ScanDirection;
  /** 光带透明度 */
  opacity: number;
  /** 光带颜色 */
  color: string;
  /** 动画缓动函数 */
  easing: string;
}

/** 默认扫光动画配置 */
export const DEFAULT_SCAN_CONFIG: ScanAnimationConfig = {
  duration: 2,
  direction: "left-to-right",
  opacity: 0.6,
  color: "rgba(255, 255, 255, 0.6)",
  easing: "ease-out",
};

/** 扫光方向对应的CSS类名映射 */
export const SCAN_DIRECTION_CLASS_MAP: Record<ScanDirection, string> = {
  "left-to-right": "scan-left-to-right",
  "right-to-left": "scan-right-to-left",
  "top-to-bottom": "scan-top-to-bottom",
  "bottom-to-top": "scan-bottom-to-top",
};

/** 扫光方向对应的动画名称映射 */
export const SCAN_ANIMATION_NAME_MAP: Record<ScanDirection, string> = {
  "left-to-right": "scanLeftToRight",
  "right-to-left": "scanRightToLeft",
  "top-to-bottom": "scanTopToBottom",
  "bottom-to-top": "scanBottomToTop",
};
