<template>
  <div class="slide-content">
    <!-- 预约按钮 -->
    <div class="reservation-btn">
      <WebM class="store-button breathe" :src="WebMReservation" :mov-src="WebMReservationMov" @click="scrollToNextSlide(true)" />
    </div>

    <!-- 居中的应用商店按钮组 -->
    <div class="app-store-buttons">
      <WebM v-for="button in visibleStoreButtons" :key="button.id" class="store-button breathe" :src="button.src" :mov-src="button.movSrc" @click="handleStoreClick(button)" />
    </div>

    <!-- 底部滚动按钮 -->
    <div class="scroll-down-wrapper">
      <img src="@/assets/imgs/p1/scroll-down.png" alt="向下滚动" class="scroll-down-button bounce" @click="scrollToNextSlide(false)" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import WebM from "@/components/WebM.vue";
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { getUseAppStore, type ToLinkCode } from "@kingnet/kingnet-pre-test";
import { useMediaButton } from "@/hooks/useMediaButton";

// WebM 资源导入
import WebMReservation from "@/assets/imgs/webm/reservation.webm";
import WebMReservationMov from "@/assets/imgs/webm/reservation.mov";
import WebMAppleStore from "@/assets/imgs/webm/apple-store.webm";
import WebMAppleStoreMov from "@/assets/imgs/webm/apple-store.mov";
import WebMGooglePlay from "@/assets/imgs/webm/google-play.webm";
import WebMGooglePlayMov from "@/assets/imgs/webm/google-play.mov";
import WebMOneStore from "@/assets/imgs/webm/one-store.webm";
import WebMOneStoreMov from "@/assets/imgs/webm/one-store.mov";
import WebMSamsungStore from "@/assets/imgs/webm/samsung-store.webm";
import WebMSamsungStoreMov from "@/assets/imgs/webm/samsung-store.mov";

// Props 定义
interface Props {
  mainSwiperInstance?: any;
}

const props = withDefaults(defineProps<Props>(), {
  mainSwiperInstance: null,
});

const store = getUseAppStore()();
const { toStore } = useMediaButton();

// 设备检测
const { isMobile, isIOS } = useDeviceDetection();

// 应用商店按钮配置
interface StoreButton {
  id: ToLinkCode;
  src: string;
  movSrc: string;
  showOnPC: boolean;
  showOnMobile: boolean;
  mobileCondition?: () => boolean;
  event: string;
  noStore?: boolean;
}

const storeButtons: StoreButton[] = [
  {
    id: "gp",
    src: WebMGooglePlay,
    movSrc: WebMGooglePlayMov,
    showOnPC: true,
    showOnMobile: true,
    mobileCondition: () => !isIOS.value,
    event: "gpkv",
  },
  {
    id: "ios",
    src: WebMAppleStore,
    movSrc: WebMAppleStoreMov,
    showOnPC: true,
    showOnMobile: true,
    mobileCondition: () => isIOS.value,
    event: "ioskv",
  },
  {
    id: "one",
    src: WebMOneStore,
    movSrc: WebMOneStoreMov,
    showOnPC: true,
    showOnMobile: false,
    event: "onekv",
    noStore: true,
  },
  {
    id: "samsung",
    src: WebMSamsungStore,
    movSrc: WebMSamsungStoreMov,
    showOnPC: true,
    showOnMobile: false,
    event: "galaxykv",
    noStore: true,
  },
];

// 计算可见的应用商店按钮
const visibleStoreButtons = computed(() => {
  return storeButtons.filter((button) => {
    if (isMobile.value) {
      return button.showOnMobile && (!button.mobileCondition || button.mobileCondition());
    } else {
      return button.showOnPC;
    }
  });
});

// 应用商店按钮点击处理
const handleStoreClick = (btn: StoreButton) => {
  if (btn.noStore) {
    store.ga4TrackEvent(btn.event);
    store.toLink({ code: btn.id });
  } else {
    toStore(btn.event, btn.id);
  }
};

// 滚动到下一屏
const scrollToNextSlide = (isReservation: boolean = false) => {
  isReservation && store.ga4TrackEvent("preregister");
  if (props.mainSwiperInstance) {
    props.mainSwiperInstance.slideTo(1); // 导航到第2屏
  } else {
    // 移动端：滚动到下一个section
    const nextSection = document.querySelector(".section-2");
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: "smooth" });
    }
  }
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.reservation-btn {
  position: absolute;
  left: 53%;
  bottom: 20%;
  width: 430px;
  cursor: pointer;

  @media (max-width: 540px) {
    left: 50%;
    bottom: 15%;
    transform: translateX(-50%);
    width: 5.58rem;
    height: auto;
  }
}

.app-store-buttons {
  position: absolute;
  bottom: 100px;
  left: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  transform: translateX(-50%);

  .store-button {
    width: 240px;
    cursor: pointer;
  }

  @media (max-width: 540px) {
    width: 3.15rem;
    height: auto;
    bottom: 8.5%;
  }
}

// 滚动按钮样式
.scroll-down-wrapper {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);

  .scroll-down-button {
    width: 121px;
    height: auto;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.3));
    }
  }

  @media (max-width: 540px) {
    bottom: 0;

    .scroll-down-button {
      width: 1.81rem;
    }
  }
}
</style>
