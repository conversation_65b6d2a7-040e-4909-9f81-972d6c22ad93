<template>
  <div class="slide-content">
    <ParticleEffect />
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p2/title.png" alt="" />
      </div>
    </transition>
    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="content">
        <!-- 预约奖励 -->
        <div class="rewards">
          <div class="reward rewards1">
            <WebM class="reward-light" :src="WebMLight" :mov-src="WebMLightMov" />
            <img src="@/assets/imgs/p2/reward1.png" alt="" />
          </div>
          <div class="reward rewards2">
            <WebM class="reward-light" :src="WebMLight" :mov-src="WebMLightMov" />
            <img src="@/assets/imgs/p2/reward2.png" alt="" />
          </div>
        </div>
        <!-- 平台选择按钮 -->
        <div class="platform-buttons">
          <button class="platform-btn aos-btn" :class="{ active: selectedPlatform === 'aos' }" @click="selectedPlatform = 'aos'"></button>
          <button class="platform-btn ios-btn" :class="{ active: selectedPlatform === 'ios' }" @click="selectedPlatform = 'ios'"></button>
        </div>
        <div class="phone-input">
          <PhoneInput v-model="phone" @blur="store.onInputBlur" @focus="store.onInputFocus" />
        </div>
        <div class="rule-btn" @click="handleRuleClick">
          <img src="@/assets/imgs/p2/rule-btn.png" alt="" />
        </div>
        <div class="reservation-btn" @click="handleReservationClick">
          <WebM class="reservation breathe" :src="WebMP2ReservationBtn" :mov-src="WebMP2ReservationBtnMov" />
        </div>
        <div class="big-reward">
          <div class="reward rewards3">
            <WebM class="reward-light" :src="WebMLight" :mov-src="WebMLightMov" />
            <img src="@/assets/imgs/p2/reward3.png" alt="" />
          </div>
        </div>
        <div class="store-buttons">
          <WebM class="store-button breathe" :src="WebMGooglePlay" :mov-src="WebMGooglePlayMov" @click="handleStoreClick('gpreservation', 'gp')" />
          <WebM class="store-button breathe" :src="WebMAppleStore" :mov-src="WebMAppleStoreMov" @click="handleStoreClick('iosreservation', 'ios')" />
        </div>
        <div class="community-btn" @click="handleCommunityClick">
          <WebM class="community breathe" :src="WebMP2CommunityBtn" :mov-src="WebMP2CommunityBtnMov" />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import WebM from "@/components/WebM.vue";
import PhoneInput from "@/components/PhoneInput.vue";
import ParticleEffect from "@/components/ParticleEffect.vue";
import { getUseAppStore, type ToLinkCode } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";

// Hooks
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { useMediaButton } from "@/hooks/useMediaButton";

// WebM 资源导入
import WebMGooglePlay from "@/assets/imgs/webm/google-play.webm";
import WebMGooglePlayMov from "@/assets/imgs/webm/google-play.mov";
import WebMAppleStore from "@/assets/imgs/webm/apple-store.webm";
import WebMAppleStoreMov from "@/assets/imgs/webm/apple-store.mov";
import WebMP2ReservationBtn from "@/assets/imgs/webm/p2-reservation-btn.webm";
import WebMP2ReservationBtnMov from "@/assets/imgs/webm/p2-reservation-btn.mov";
import WebMP2CommunityBtn from "@/assets/imgs/webm/p2-community-btn.webm";
import WebMP2CommunityBtnMov from "@/assets/imgs/webm/p2-community-btn.mov";
import WebMLight from "@/assets/imgs/webm/light.webm";
import WebMLightMov from "@/assets/imgs/webm/light.mov";

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
});

// Emit 定义
const emit = defineEmits<{
  openPrivacyPopup: [];
}>();

const store = getUseAppStore()();
const { phone, businessConfig } = storeToRefs(store);

const { toCommunity, toStore } = useMediaButton();

// 设备检测
const { deviceType } = useDeviceDetection();

// 平台选择 - 根据设备类型初始化
const selectedPlatform = ref<"aos" | "ios">(deviceType.value === "ios" ? "ios" : "aos");

// 应用商店按钮点击处理
const handleStoreClick = (eventName: string, code: ToLinkCode) => {
  toStore(eventName, code);
};

// 规则按钮点击处理
const handleRuleClick = () => {
  emit("openPrivacyPopup");
};

// 社区按钮点击处理
const handleCommunityClick = () => {
  toCommunity("loungereservation");
};

// 预约按钮点击处理
const handleReservationClick = async () => {
  store.ga4TrackEvent("reservation");
  const { success } = await store.toOrder(false, [
    { eventName: "allappointment", deduplicationType: "none" },
    { eventName: "allappointmentonly", deduplicationType: "ip" },
  ]);
  if (success) {
    businessConfig.value.showPopup("store");
  }
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
}

.content {
  position: absolute;
  top: 170px;
  margin-left: -50px;
  width: 1239px;
  height: 776px;
  background: url(@/assets/imgs/p2/box-bg.png);
  display: flex;
  align-items: center;
  justify-content: center;

  // 通用奖励样式
  .reward {
    position: relative;
    pointer-events: none;

    &.rewards1 {
      width: 300px;
      height: 274px;
    }

    &.rewards2 {
      width: 229px;
      height: 199px;
    }

    &.rewards3 {
      width: 464px;
      height: 193px;
    }

    .reward-light {
      position: absolute;
      width: 600px;
      height: auto;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    img {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }
  }

  .rewards {
    position: absolute;
    top: 180px;
    left: 105px;
    display: flex;
    align-items: center;
  }

  .platform-buttons {
    position: absolute;
    top: 470px;
    left: 290px;
    display: flex;
    gap: 80px;
    align-items: center;
    justify-content: center;

    .platform-btn {
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;
      width: 53px;
      height: 16px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;

      &.aos-btn {
        background-image: url(@/assets/imgs/p2/aos.png);

        &.active {
          background-image: url(@/assets/imgs/p2/aos-active.png);
        }
      }

      &.ios-btn {
        background-image: url(@/assets/imgs/p2/ios.png);

        &.active {
          background-image: url(@/assets/imgs/p2/ios-active.png);
        }
      }
    }
  }

  .phone-input {
    position: absolute;
    top: 500px;
    left: 170px;
    width: 420px;
  }

  .rule-btn {
    position: absolute;
    top: 556px;
    left: 540px;
    cursor: pointer;
  }

  .reservation-btn {
    position: absolute;
    bottom: 70px;
    left: 210px;
    width: 350px;
    cursor: pointer;
  }

  .big-reward {
    position: absolute;
    top: 180px;
    right: 90px;
  }

  .store-buttons {
    position: absolute;
    right: 115px;
    top: 375px;
    display: flex;
    align-items: center;
    justify-content: center;

    .store-button {
      width: 220px;
      cursor: pointer;
    }
  }

  .community-btn {
    position: absolute;
    bottom: 70px;
    right: 155px;
    width: 350px;
    cursor: pointer;
  }
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 4rem;
        height: auto;
      }
    }

    .content {
      top: 1.1rem;
      margin: 0 auto;
      margin-left: 0.2rem;
      width: 7.05rem;
      height: 12.1rem;
      background: url(@/assets/imgs/p2/m/box-bg.png) no-repeat 100%/100%;

      // 通用奖励样式
      .reward {
        &.rewards1 {
          width: 3rem;
          height: 2.74rem;
        }

        &.rewards2 {
          width: 2.29rem;
          height: 1.99rem;
        }

        &.rewards3 {
          width: 4.64rem;
          height: 1.93rem;
        }

        .reward-light {
          width: 6rem;
        }

        img {
          width: 100%;
          height: 100%;
        }
      }

      .rewards {
        top: 1.5rem;
        left: 50%;
        transform: translateX(-50%);
        margin-left: -0.15rem;
      }

      .platform-buttons {
        top: 4rem;
        left: 50%;
        transform: translateX(-50%);
        gap: 0.8rem;
        margin-left: -0.1rem;

        .platform-btn {
          width: 0.53rem;
          height: 0.16rem;
        }
      }

      .phone-input {
        top: 4.25rem;
        left: 50%;
        width: 4.5rem;
        transform: translateX(-50%);
        margin-left: -0.1rem;
      }

      .rule-btn {
        top: 4.85rem;
        left: 5.2rem;

        img {
          width: 0.5rem;
          height: auto;
        }
      }

      .reservation-btn {
        top: 5rem;
        left: 50%;
        height: 1.3rem;
        transform: translateX(-50%);
        margin-left: -0.1rem;

        .reservation {
          width: 3.5rem;
          height: auto;
        }
      }

      .big-reward {
        top: 6.5rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);
      }

      .store-buttons {
        top: 8.1rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);
        margin-left: -0.1rem;
        gap: 0.1rem;

        .store-button {
          width: 2.2rem;
        }
      }

      .community-btn {
        bottom: 0.4rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);
        margin-left: -0.13rem;

        .community {
          width: 3.7rem;
          height: auto;
        }
      }
    }
  }
}
</style>
