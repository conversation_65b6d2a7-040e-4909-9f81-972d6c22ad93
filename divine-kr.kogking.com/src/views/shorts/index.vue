<template>
  <div v-if="isInitialized" class="home-view" :class="[route.name]">
    <Order />
  </div>
</template>

<script setup lang="ts">
import { ref, markRaw } from "vue";
import OrderAfterPopup from "@/views/shorts/OrderAfterPopup.vue";
import Order from "@/views/shorts/Order.vue";

// Hooks
import { useWarmupPage } from "@/hooks/useWarmupPage";
import { useRoute } from "vue-router";

// 初始化状态
const isInitialized = ref(false);
const route = useRoute();

// 预热页初始化
useWarmupPage([
  {
    type: "store",
    children: [
      {
        type: "order_after",
        weight: 1000,
        showCloseTime: 0,
        delayTime: 0,
        allowOverlayClose: false,
        eventName: {
          show: `${String(route.name)}_order_after_popup_show`,
          close: `${String(route.name)}_order_after_popup_close`,
        },
        components: markRaw(OrderAfterPopup),
      },
    ],
  },
]).then(() => {
  isInitialized.value = true;
});
</script>

<style lang="less" scoped>
.home-view {
  position: relative;
  width: 7.5rem;
  height: 100vh;
  background: url(@/assets/imgs/shorts/bg.jpg) #0a0907 no-repeat top / contain;
}
</style>
