<template>
  <PhoneInput class="phone-input" v-model="phone" />
  <div class="confirm-btn" @click="handleReservationClick">
    <div class="confirm-btn-inner breathe"></div>
  </div>
  <div class="store-btn" @click="handleStoreClick">
    <div class="store-btn-inner breathe" :class="[isIOS ? 'ios' : 'gp']"></div>
  </div>
</template>

<script setup lang="ts">
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";
import PhoneInput from "@/components/PhoneInput.vue";

// Hooks
import { useDeviceDetection } from "@/hooks/useDeviceDetection";

const { isIOS } = useDeviceDetection();
const store = getUseAppStore()();
const { businessConfig, phone } = storeToRefs(store);

// 预约按钮点击处理
const handleReservationClick = async () => {
  const { success } = await store.toOrder(false, [], [prizeConfig.zykr_order_pet.id]);
  if (success) {
    businessConfig.value.showPopup("store");
  }
};

// 商店按钮点击处理
const handleStoreClick = () => {
  const store = getUseAppStore()();
  store.toLink({ code: isIOS.value ? "ios" : "gp" });
};
</script>

<style lang="less" scoped>
.phone-input {
  position: absolute;
  top: 9.3rem;
  left: 50%;
  transform: translateX(-50%);
  width: 5rem;

  :deep(.phone-input-wrapper) {
    background: #cbcbcb;
  }

  :deep(.area-code) {
    color: #161616;
    font-size: 0.3rem;
  }

  :deep(.phone-input) {
    color: #666;
    font-size: 0.3rem;

    &::placeholder {
      color: #666;
      font-size: 0.25rem;
    }
  }

  &.error {
    :deep(.phone-input) {
      color: #ff0000;
    }
  }
}

.confirm-btn {
  position: absolute;
  top: 10rem;
  left: 50%;
  transform: translateX(-50%);

  .confirm-btn-inner {
    width: 5.79rem;
    height: 1.34rem;
    background: url(@/assets/imgs/shorts/reserve-btn.png) no-repeat 100%/100%;
    cursor: pointer;
  }
}

.store-btn {
  position: absolute;
  top: 11.8rem;
  left: 50%;
  transform: translateX(-50%);

  .store-btn-inner {
    width: 2.28rem;
    height: 0.71rem;
    background: no-repeat 100%/100%;
    cursor: pointer;

    &.ios {
      background-image: url(@/assets/imgs/shorts/ios-btn.png);
    }

    &.gp {
      background-image: url(@/assets/imgs/shorts/gp-btn.png);
    }
  }
}
</style>
