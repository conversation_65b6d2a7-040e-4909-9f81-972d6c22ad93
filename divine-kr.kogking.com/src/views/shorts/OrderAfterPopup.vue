<template>
  <div class="bg">
    <div class="code">
      <input type="text" v-model="codeText" readonly />
      <div class="copy-btn" @click="handleCopy"></div>
    </div>
    <div class="store-btns">
      <div class="store-btn breathe" :class="[`${isIOS ? 'apple' : 'google'}`]" @click="handleStoreClick"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { getUseAppStore, AppUtils } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";

const emit = defineEmits<{
  close: [];
}>();

const store = getUseAppStore()();
const { orderResult } = storeToRefs(store);

const { isIOS } = useDeviceDetection();

const codeText = computed(() => {
  return orderResult.value?.packages.find((item: any) => item.name === prizeConfig.zykr_order_pet.name)?.package_num;
});

const handleCopy = () => {
  AppUtils.copy(codeText.value);
};

// 商店按钮点击处理
const handleStoreClick = () => {
  store.toLink({ code: isIOS.value ? "ios" : "gp" });
  emit("close");
};
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 698px;
  height: 685px;
  background: url("@/assets/imgs/pop/order-after/bg.png") no-repeat;
  background-size: 100% 100%;

  .code {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    top: 118px;
    right: 100px;
    height: 35px;
    gap: 15px;

    input {
      width: 185px;
      padding: 0 10px;
      color: #fff;
      text-indent: 0;
      font-size: 16px;
      text-align: center;
      background: none;
    }

    .copy-btn {
      width: 98px;
      height: 39px;
      background: url("@/assets/imgs/pop/copy-btn.png") no-repeat 100%/100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: brightness(1.1);
      }

      &.copied {
        filter: brightness(1.2) drop-shadow(0 0 5px #4caf50);
      }
    }
  }

  .store-btns {
    position: absolute;
    bottom: 40px;
    right: 80px;

    .store-btn {
      width: 291px;
      height: 166px;
      cursor: pointer;
      transition: all 0.3s ease;
      background-size: 100%;

      &.apple {
        background-image: url(@/assets/imgs/pop/order-after/apple-store-btn.png);
      }

      &.google {
        background-image: url(@/assets/imgs/pop/order-after/google-play-btn.png);
      }

      &:hover {
        filter: drop-shadow(0 0 10px #7f69b3);
      }
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.98rem;
    height: 6.85rem;

    .code {
      top: 1.18rem;
      right: 1rem;
      height: 0.35rem;
      gap: 0.15rem;

      input {
        width: 1.85rem;
        padding: 0 0.1rem;
        font-size: 0.18rem;
      }

      .copy-btn {
        width: 0.98rem;
        height: 0.39rem;
      }
    }

    .store-btns {
      bottom: 0.4rem;
      right: 0.8rem;

      .store-btn {
        width: 2.91rem;
        height: 1.66rem;
      }
    }
  }
}
</style>
