<template>
  <div class="webm-player">
    <video v-if="!error" ref="videoRef" :src="videoSrc" autoplay muted loop disablepictureinpicture disableremoteplayback x-webkit-airplay="deny" playsinline class="webm-video" @error="onError" />
    <img v-else-if="fallback" :src="fallback" class="fallback" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useDeviceDetection } from "@/hooks/useDeviceDetection";

interface Props {
  src: string | { default: string };
  movSrc?: string | { default: string };
  fallback?: string;
}

const props = withDefaults(defineProps<Props>(), {});

const videoRef = ref<HTMLVideoElement>();
const error = ref(false);
const { isIOS } = useDeviceDetection();

// 根据设备和浏览器类型动态选择视频源
const videoSrc = computed(() => {
  // 如果是iOS设备（包括Safari浏览器），优先使用mov文件
  if (isIOS.value && props.movSrc) {
    if (typeof props.movSrc === "object" && props.movSrc.default) {
      return props.movSrc.default;
    } else if (typeof props.movSrc === "string") {
      return props.movSrc;
    }
  }

  // 否则使用webm文件
  if (!props.src) return "";

  // 处理模块导入的情况
  if (typeof props.src === "object" && props.src.default) {
    return props.src.default;
  } else if (typeof props.src === "string") {
    return props.src;
  }

  return "";
});

const onError = () => {
  error.value = true;
};

onMounted(() => {
  if (videoRef.value && videoSrc.value) {
    videoRef.value.load();
  }
});
</script>

<style scoped>
.webm-player {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.webm-video,
.fallback {
  width: 100%;
  height: auto;
  max-width: 100%;
  pointer-events: none;
  user-select: none;
}

.error {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  font-size: 14px;
}
</style>
