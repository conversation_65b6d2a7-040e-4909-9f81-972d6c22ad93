<template>
  <nav v-if="!isMobile" class="sidebar-navigation" role="navigation" aria-label="主导航">
    <ul class="nav-list">
      <li v-for="(item, index) in navigationItems" :key="index" class="nav-item"
        :class="{ active: activeIndex === index }" :data-index="index + 1" :aria-label="item.label"
        @click="handleNavigate(index)">
      </li>
    </ul>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useIsMobile } from '@/hooks/useIsMobile'

// Props 接口定义
interface Props {
  currentSlide: number // 当前slide索引（0-6）
  activeTab: string // 当前激活的tab（'tab1' | 'tab2'）
  mainSwiperInstance?: any // 主swiper实例
}

// Emits 接口定义
interface Emits {
  (e: 'slide-change', slideIndex: number): void // slide切换事件
  (e: 'tab-change', tab: string): void // tab切换事件
}

// 定义 props 和 emits
const props = withDefaults(defineProps<Props>(), {
  currentSlide: 0,
  activeTab: 'tab1',
  mainSwiperInstance: null
})

const emit = defineEmits<Emits>()

const { isMobile } = useIsMobile()

// 导航项配置
const navigationItems = [
  { label: '首页' },
  { label: '预约' },
  { label: '抽奖' },
  { label: '里程碑' },
  { label: '职业介绍' },
  { label: '公会' },
  { label: '世界观' },
  { label: '游戏介绍' }
]

// 计算当前激活的导航项索引
const activeIndex = computed(() => {
  // 导航项1-6（索引0-5）：对应slide 0-5
  if (props.currentSlide <= 5) {
    return props.currentSlide
  }

  // 导航项7-8（索引6-7）：都对应slide 6，但根据activeTab区分
  if (props.currentSlide === 6) {
    return props.activeTab === 'tab1' ? 6 : 7
  }

  return 0
})

// 处理导航点击事件
const handleNavigate = (index: number) => {
  if (index !== activeIndex.value) {
    // 导航项1-6（索引0-5）：直接切换到对应的slide
    if (index <= 5) {
      if (props.mainSwiperInstance) {
        props.mainSwiperInstance.slideTo(index)
      }
      emit('slide-change', index)
    }
    // 导航项7（索引6）：切换到slide 6，并设置为tab1
    else if (index === 6) {
      if (props.mainSwiperInstance) {
        props.mainSwiperInstance.slideTo(6)
      }
      emit('slide-change', 6)
      emit('tab-change', 'tab1')
    }
    // 导航项8（索引7）：切换到slide 6，并设置为tab2
    else if (index === 7) {
      if (props.mainSwiperInstance) {
        props.mainSwiperInstance.slideTo(6)
      }
      emit('slide-change', 6)
      emit('tab-change', 'tab2')
    }
  }
}
</script>

<style lang="less" scoped>
.sidebar-navigation {
  position: fixed;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;

  @media (max-width: 750px) {
    display: none; // 在小屏幕上隐藏
  }
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 35px;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 123px;
  height: 22px;
  cursor: pointer;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  filter: brightness(1.5);

  // 默认状态背景图
  &[data-index="1"] {
    background-image: url('@/assets/imgs/sidebar/normal/1.png');
  }

  &[data-index="2"] {
    background-image: url('@/assets/imgs/sidebar/normal/2.png');
  }

  &[data-index="3"] {
    background-image: url('@/assets/imgs/sidebar/normal/3.png');
  }

  &[data-index="4"] {
    background-image: url('@/assets/imgs/sidebar/normal/4.png');
  }

  &[data-index="5"] {
    background-image: url('@/assets/imgs/sidebar/normal/5.png');
  }

  &[data-index="6"] {
    background-image: url('@/assets/imgs/sidebar/normal/6.png');
  }

  &[data-index="7"] {
    background-image: url('@/assets/imgs/sidebar/normal/7.png');
  }

  &[data-index="8"] {
    background-image: url('@/assets/imgs/sidebar/normal/8.png');
  }

  // 激活状态
  &.active {
    width: 219px;
    height: 93px;
    margin: -35px 0 -20px;
    margin-left: -42px;
    filter: brightness(1);

    // 激活状态背景图
    &[data-index="1"] {
      background-image: url('@/assets/imgs/sidebar/active/1.png');
    }

    &[data-index="2"] {
      background-image: url('@/assets/imgs/sidebar/active/2.png');
    }

    &[data-index="3"] {
      background-image: url('@/assets/imgs/sidebar/active/3.png');
    }

    &[data-index="4"] {
      background-image: url('@/assets/imgs/sidebar/active/4.png');
    }

    &[data-index="5"] {
      background-image: url('@/assets/imgs/sidebar/active/5.png');
    }

    &[data-index="6"] {
      background-image: url('@/assets/imgs/sidebar/active/6.png');
    }

    &[data-index="7"] {
      background-image: url('@/assets/imgs/sidebar/active/7.png');
    }

    &[data-index="8"] {
      background-image: url('@/assets/imgs/sidebar/active/8.png');
    }
  }

  // 悬停效果
  &:hover {
    transform: translateX(2px);
    filter: brightness(1.7);
  }
}
</style>
