<template>
  <Popup ref="popup" content-top="52%">
    <div class="bg">
      <div class="close" @click="handleClose"></div>

      <!-- 公会名称输入框 -->
      <div class="guild-name">
        <input type="text" v-model="guildName" :placeholder="'길드 이름을 10자 이내로 입력하세요'" maxlength="10"
          @input="validateGuildName" />
      </div>

      <!-- 公会类型下拉选择器 -->
      <div class="guild-type">
        <div class="select-wrapper" @click="toggleDropdown">
          <div class="select-display">
            <span :class="{ placeholder: !selectedType }">
              {{ selectedType || '클릭하여 길드 플레이 스타일을 선택하세요' }}
            </span>
            <div class="arrow" :class="{ open: isDropdownOpen }"></div>
          </div>
          <div class="dropdown" v-show="isDropdownOpen">
            <div class="option" v-for="option in guildTypes" :key="option.value" @click.stop="selectType(option)">
              {{ option.label }}
            </div>
          </div>
        </div>
      </div>

      <!-- 公会群聊链接输入框 -->
      <div class="guild-link">
        <input type="text" v-model="guildLink" :placeholder="'카카오톡 오픈채팅방 링크를 입력하세요'" @input="validateGuildLink"
          @blur="formatGuildLink" />
        <div class="error" v-if="linkError">{{ linkError }}</div>
      </div>

      <div class="confirm-btn" @click="handleConfirm"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import Popup from './Popup.vue'
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";

interface GuildType {
  value: string
  label: string
}

const popup = ref<InstanceType<typeof Popup> | null>(null)
const emit = defineEmits(['createGuild'])

const store = getUseAppStore()();

// 表单数据
const guildName = ref('')
const selectedType = ref('')
const guildLink = ref('')
const linkError = ref('')

// 下拉框状态
const isDropdownOpen = ref(false)

// 公会类型选项
const guildTypes: GuildType[] = [
  { value: 'hardcore', label: '하드코어 (경기 도전)' },
  { value: 'casual', label: '캐주얼 (소셜)' }
]

onMounted(() => {
  // 点击外部关闭下拉框
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const open = () => {
  popup.value?.open()
}

const handleClose = () => {
  popup.value?.close()
}

// 公会名称验证（10字符限制）
const validateGuildName = () => {
  if (guildName.value.length > 10) {
    guildName.value = guildName.value.slice(0, 10)
  }
}

// 下拉框切换
const toggleDropdown = (event: Event) => {
  event.stopPropagation()
  isDropdownOpen.value = !isDropdownOpen.value
}

// 选择公会类型
const selectType = (option: GuildType, event?: Event) => {
  if (event) {
    event.stopPropagation()
  }
  selectedType.value = option.label
  isDropdownOpen.value = false
}

// 点击外部关闭下拉框
const handleClickOutside = () => {
  isDropdownOpen.value = false
}

// 验证KakaoTalk链接
const validateGuildLink = () => {
  linkError.value = ''

  if (guildLink.value && !guildLink.value.startsWith('https://open.kakao.com/')) {
    linkError.value = 'KakaoTalk 오픈채팅방 링크만 입력 가능합니다.'
  }
}

// 格式化链接（确保正确的前缀）
const formatGuildLink = () => {
  if (guildLink.value && !guildLink.value.startsWith('https://')) {
    if (guildLink.value.includes('open.kakao.com')) {
      guildLink.value = 'https://' + guildLink.value.replace(/^https?:\/\//, '')
    }
  }
  validateGuildLink()
}

// 确认按钮处理
const handleConfirm = () => {
  // 验证所有字段
  if (!guildName.value.trim()) {
    return
  }

  if (!selectedType.value) {
    return
  }

  if (!guildLink.value.trim()) {
    return
  }

  if (linkError.value) {
    return
  }

  const guildData = {
    camp_name: guildName.value.trim(),
    camp_type: selectedType.value,
    social_link: guildLink.value.trim()
  }
  createGuild(guildData)
}

async function createGuild(data: any) {
  const { phone, areaCode } = storeToRefs(store);

  const res = await store.ApiPost('/website/camps/create', {
    ...data,
    phone: phone.value,
    dialing_code: areaCode.value,
  })

  if (res.code === 0) {
    handleClose()
    emit('createGuild', res.data)
  } else {
    store.showAlert(res.message)
  }
}

defineExpose({
  open
})
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 616px;
  height: 820px;
  background: url("@/assets/imgs/pop/guild/bg.png") no-repeat;
  background-size: 100% 100%;

  .close {
    position: absolute;
    top: -60px;
    right: -10px;
    width: 60px;
    height: 59px;
    background: url("@/assets/imgs/pop/close.png") no-repeat 100%/100%;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #b294bc);
    }
  }

  input {
    position: absolute;
    width: 100%;
    height: 50px;
    padding: 0 20px;
    border-radius: 8px;
    background: none;
    font-size: 25px;
    color: #fff;
    outline: none;
    transition: all 0.3s ease;
    text-indent: 0;

    &::placeholder {
      color: #fff;
      font-size: 25px;
    }
  }

  // 公会名称输入框
  .guild-name {
    position: absolute;
    top: 115px;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;
  }

  // 公会类型下拉选择器
  .guild-type {
    position: absolute;
    top: 293px;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;

    .select-wrapper {
      position: relative;
      cursor: pointer;

      .select-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 50px;
        padding: 0 20px;
        border-radius: 8px;
        background: none;
        font-size: 25px;
        color: #fff;
        transition: all 0.3s ease;

        span {
          flex: 1;

          &.placeholder {
            color: #fff;
            font-size: 20px;
          }
        }

        .arrow {
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 8px solid #666;
          transition: transform 0.3s ease;

          &.open {
            transform: rotate(180deg);
          }
        }
      }

      .dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #a5cbd1;
        border-top: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;

        .option {
          padding: 15px 20px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          color: #fff;
          font-size: 18px;

          &:hover {
            background-color: #a0c3c2
          }
        }
      }
    }
  }

  // 公会链接输入框
  .guild-link {
    position: absolute;
    top: 472px;
    left: 50%;
    transform: translateX(-50%);
    width: 400px;

    .error {
      margin-top: 85px;
      padding: 5px 10px;
      background: rgba(255, 0, 0, 0.1);
      border: 1px solid #ff4444;
      border-radius: 4px;
      color: #cc0000;
      font-size: 16px;
    }
  }

  .confirm-btn {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    width: 285px;
    height: 84px;
    background: url("@/assets/imgs/pop/guild/confirm-btn.png") no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      filter: brightness(1.1) drop-shadow(0 0 10px #3fdad5);
      transform: translateX(-50%) scale(1.02);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.16rem;
    height: 8.2rem;

    .close {
      top: -0.6rem;
      right: -0.1rem;
      width: 0.6rem;
      height: 0.59rem;
    }

    input {
      height: 0.5rem;
      padding: 0 0.2rem;
      font-size: 0.25rem;

      &::placeholder {
        font-size: 0.25rem;
      }
    }

    // 公会名称输入框
    .guild-name {
      top: 1.15rem;
      width: 4rem;
      height: 0.5rem;
    }

    // 公会类型下拉选择器
    .guild-type {
      top: 2.93rem;
      width: 4rem;

      .select-wrapper {
        .select-display {
          height: 0.5rem;
          padding: 0 0.2rem;
          font-size: 0.25rem;

          span {
            &.placeholder {
              font-size: 0.2rem;
            }
          }

          .arrow {
            border-left: 0.06rem solid transparent;
            border-right: 0.06rem solid transparent;
            border-top: 0.08rem solid #666;
          }
        }

        .dropdown {
          border-radius: 0.08rem;

          .option {
            padding: 0.15rem 0.2rem;
            font-size: 0.25rem;
          }
        }
      }
    }

    // 公会链接输入框
    .guild-link {
      top: 4.72rem;
      width: 4rem;
      height: 0.5rem;


      .error {
        margin-top: 0.85rem;
        padding: 0.05rem 0.1rem;
        border-radius: 0.04rem;
        font-size: 0.16rem;
      }
    }

    .confirm-btn {
      bottom: 0.4rem;
      width: 2.85rem;
      height: 0.84rem;

      &:hover {
        transform: translateX(-50%) scale(1.02);
      }
    }
  }
}
</style>
