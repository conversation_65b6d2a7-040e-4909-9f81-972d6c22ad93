<template>
  <div class="bg">
    <div class="code">
      <input type="text" v-model="codeText" readonly />
      <div class="copy-btn" @click="handleCopy"></div>
    </div>
    <div class="community" @click="handleCommunity">
      <div class="community-btn breathe"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { getUseAppStore, AppUtils } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";
import { useMediaButton } from "@/hooks/useMediaButton";

const emit = defineEmits<{
  close: [];
  next: [];
}>();

const store = getUseAppStore()();
const { orderResult } = storeToRefs(store);

const { toCommunity } = useMediaButton();

const codeText = computed(() => {
  return orderResult.value?.packages.find((item: any) => item.name === prizeConfig.zykr_store_xq.name)?.package_num;
});

const handleCopy = () => {
  AppUtils.copy(codeText.value);
};

const handleCommunity = () => {
  toCommunity("loungesucceed");
  store.grant(prizeConfig.zykr_community_growup.id);
  emit("next");
};
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 698px;
  height: 685px;
  background: url("@/assets/imgs/pop/order-store/bg.png") no-repeat;
  background-size: 100% 100%;

  .code {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    top: 128px;
    right: 100px;
    height: 35px;
    gap: 15px;

    input {
      width: 185px;
      padding: 0 10px;
      color: #fff;
      text-indent: 0;
      font-size: 16px;
      text-align: center;
      background: none;
    }

    .copy-btn {
      width: 98px;
      height: 39px;
      background: url("@/assets/imgs/pop/copy-btn.png") no-repeat 100%/100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: brightness(1.1);
      }

      &.copied {
        filter: brightness(1.2) drop-shadow(0 0 5px #4caf50);
      }
    }
  }

  .community {
    position: absolute;
    bottom: 25px;
    right: 105px;

    .community-btn {
      width: 179px;
      height: 95px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: url("@/assets/imgs/pop/order-store/community-btn.png") no-repeat 100%/100%;

      &:hover {
        filter: drop-shadow(0 0 10px #7f69b3);
      }
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.98rem;
    height: 6.85rem;

    .code {
      top: 1.28rem;
      right: 1rem;
      height: 0.35rem;
      gap: 0.15rem;

      input {
        width: 1.85rem;
        padding: 0 0.1rem;
        font-size: 0.18rem;
      }

      .copy-btn {
        width: 0.98rem;
        height: 0.39rem;
      }
    }

    .community {
      bottom: 0.25rem;
      right: 1.05rem;

      .community-btn {
        width: 1.79rem;
        height: 0.95rem;
      }
    }
  }
}
</style>
