<template>
  <Popup ref="popup">
    <div class="bg">
      <div class="close" @click="handleClose"></div>
      <div class="code">
        <input type="text" v-model="codeText" readonly />
        <div class="copy-btn" @click="handleCopy"></div>
      </div>
      <div class="code link">
        <input type="text" v-model="codeLink" readonly />
        <div class="copy-btn" @click="handleCopyLink"></div>
      </div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import Popup from "./Popup.vue";
import { getUseAppStore, AppUtils } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";

const popup = ref<InstanceType<typeof Popup> | null>(null);
const store = getUseAppStore()();
const { orderResult } = storeToRefs(store);

const codeText = computed(() => {
  return orderResult.value?.packages.find((item: any) => item.name === prizeConfig.zykr_guild_growup.name)?.package_num;
});

const codeLink = ref("");

const open = (link?: string) => {
  store.ga4TrackEvent("guildresult");
  store.ga4TrackEvent("firstguildresult", {}, "browser");
  if (link) {
    codeLink.value = link;
  }
  popup.value?.open();
};

const handleClose = () => {
  popup.value?.close();
};

const handleCopyLink = () => {
  AppUtils.copy(codeLink.value);
};

const handleCopy = () => {
  AppUtils.copy(codeText.value);
};

defineExpose({
  open,
});
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 613px;
  height: 555px;
  background: url("@/assets/imgs/pop/guild/prize-bg.png") no-repeat;
  background-size: 100% 100%;

  .close {
    position: absolute;
    top: -60px;
    right: -10px;
    width: 60px;
    height: 59px;
    background: url("@/assets/imgs/pop/close.png") no-repeat 100%/100%;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #b294bc);
    }
  }

  .code {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    bottom: 91px;
    right: 50px;
    height: 35px;
    gap: 15px;

    input {
      width: 210px;
      padding: 0 10px;
      color: #fff;
      text-indent: 0;
      font-size: 16px;
      text-align: center;
      background: none;
    }

    .copy-btn {
      width: 98px;
      height: 39px;
      background: url("@/assets/imgs/pop/guild/prize-copy-btn.png") no-repeat 100%/100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: brightness(1.1);
      }

      &.copied {
        filter: brightness(1.2) drop-shadow(0 0 5px #4caf50);
      }
    }

    &.link {
      bottom: 38px;

      input {
        width: 400px;
      }

      .copy-btn {
        background-image: url("@/assets/imgs/pop/guild/link-copy-btn.png");
      }
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.13rem;
    height: 5.55rem;

    .close {
      top: -0.6rem;
      right: -0.1rem;
      width: 0.6rem;
      height: 0.59rem;
    }

    .code {
      bottom: 0.91rem;
      right: 0.5rem;
      height: 0.35rem;
      gap: 0.15rem;

      input {
        width: 2.1rem;
        padding: 0 0.1rem;
        font-size: 0.18rem;
      }

      .copy-btn {
        width: 0.98rem;
        height: 0.39rem;
      }

      &.link {
        bottom: 0.38rem;

        input {
          width: 4rem;
        }
      }
    }
  }
}
</style>
