<template>
  <Popup ref="popup">
    <div class="bg">
      <div class="content">
        <div class="content-text">
          <template v-for="(item, index) in prizeItems" :key="index">
            <!-- 标题 -->
            <div class="title">
              <img :src="item.titleImage">
            </div>

            <!-- 兑换码区域 -->
            <div class="code" :class="item.codeClass">
              <input type="text" :value="item.code || ''" readonly>
              <div class="btn" :class="item.btnType" @click="handleButtonClick(item, 'code')"></div>
            </div>

            <!-- 抽奖区域（如果存在） -->
            <div v-if="item.lotteryClass" class="lottery" :class="item.lotteryClass">
              <div class="btn lottery" @click="handleButtonClick(item, 'lottery')"></div>
            </div>
          </template>
        </div>
      </div>
      <div class="confirm-btn close"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import Popup from './Popup.vue'
import { getUseAppStore, AppUtils } from "@kingnet/kingnet-pre-test"
import { storeToRefs } from "pinia"
import { prizeConfig, PrizeKey } from '@/config/prizeConfig'

// 导入标题图片资源
import Title1 from '@/assets/imgs/pop/prize-list/title1.png'
import Title2 from '@/assets/imgs/pop/prize-list/title2.png'
import Title3 from '@/assets/imgs/pop/prize-list/title3.png'
import Title4 from '@/assets/imgs/pop/prize-list/title4.png'
import Title5 from '@/assets/imgs/pop/prize-list/title5.png'

interface PrizeItem {
  id: string
  titleImage: string
  codeClass: string
  btnType: string
  lotteryClass?: string
  configKey: PrizeKey
  code?: string
  jumpTarget?: string  // 跳转目标
}

// Props 定义
interface Props {
  mainSwiperInstance: any  // Swiper实例，用于PC端页面跳转
}

const props = withDefaults(defineProps<Props>(), {
  mainSwiperInstance: null
})

// Emit 定义
const emit = defineEmits<{
  jumpToPage: [page: string]
}>()

const popup = ref<InstanceType<typeof Popup> | null>(null)
const store = getUseAppStore()()
const { orderResult } = storeToRefs(store)

// 获取兑换码的通用函数
const getCodeByConfig = (configKey: PrizeKey) => {
  // 直接返回当前值，而不是computed，确保每次都是最新的
  return orderResult.value?.packages.find((item: any) =>
    item.name === prizeConfig[configKey].name
  )?.package_num
}

// 奖品配置数组
const prizeItems = computed<PrizeItem[]>(() => {
  const items = [
    {
      id: 'mobile',
      titleImage: Title1,
      codeClass: 'code1',
      lotteryClass: 'lottery1',
      configKey: PrizeKey.zykr_order_pet,
      jumpTarget: 'P2'
    },
    {
      id: 'store',
      titleImage: Title2,
      codeClass: 'code2',
      lotteryClass: 'lottery2',
      configKey: PrizeKey.zykr_store_xq,
      jumpTarget: 'P2'
    },
    {
      id: 'community',
      titleImage: Title3,
      codeClass: 'code3',
      lotteryClass: 'lottery3',
      configKey: PrizeKey.zykr_community_growup,
      jumpTarget: 'community'
    },
    {
      id: 'event',
      titleImage: Title4,
      codeClass: 'code4',
      configKey: PrizeKey.zykr_event_growup,
      jumpTarget: 'P3'
    },
    {
      id: 'guild',
      titleImage: Title5,
      codeClass: 'code5',
      lotteryClass: 'lottery5',
      configKey: PrizeKey.zykr_guild_growup,
      jumpTarget: 'P6'
    }
  ]

  // 为每个item添加动态属性
  return items.map(item => {
    const code = getCodeByConfig(item.configKey)
    return {
      ...item,
      code,
      btnType: code ? 'copy' : 'get'  // 有码显示copy，无码显示get
    }
  })
})

// 按钮点击处理
const handleButtonClick = (item: PrizeItem, type: 'code' | 'lottery') => {
  if (type === 'code') {
    if (item.btnType === 'copy' && item.code) {
      // 复制兑换码
      AppUtils.copy(item.code)
    } else if (item.btnType === 'get') {
      // 跳转到相应页面获取兑换码
      handleJumpToTarget(item.jumpTarget || '')
    }
  } else if (type === 'lottery') {
    // 所有lottery按钮都跳转到社群
    store.toLink({ code: "community" })
  }
}

// 页面跳转映射配置
const pageJumpConfig = {
  P2: { slideIndex: 1, sectionClass: '.section-2' },
  P3: { slideIndex: 2, sectionClass: '.section-3' },
  P6: { slideIndex: 5, sectionClass: '.section-6' }
}

// 处理跳转逻辑
const handleJumpToTarget = (target: string) => {
  // 先关闭弹窗
  popup.value?.close()

  if (target === 'community') {
    // 跳转到社群
    store.toLink({ code: "community" })
  } else if (target in pageJumpConfig) {
    // 跳转到指定页面，兼容PC和移动端
    jumpToPage(target as keyof typeof pageJumpConfig)
  }
}

// 页面跳转函数（兼容PC和移动端）
const jumpToPage = (page: keyof typeof pageJumpConfig) => {
  const config = pageJumpConfig[page]

  if (props.mainSwiperInstance) {
    // PC端：使用Swiper导航
    props.mainSwiperInstance.slideTo(config.slideIndex)
  } else {
    // 移动端：使用scrollIntoView滚动
    const targetSection = document.querySelector(config.sectionClass)
    if (targetSection) {
      targetSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // 发出事件通知（用于其他可能的处理）
  emit('jumpToPage', page)
}

const open = () => {
  // 直接打开弹窗，computed会自动获取最新的orderResult数据
  popup.value?.open()
}

defineExpose({
  open
})
</script>

<style scoped lang="less">
.bg {
  width: 659px;
  height: 900px;
  padding: 40px 25px 0 40px;
  background: url("@/assets/imgs/pop/prize-list/bg.png") no-repeat;
  background-size: 100% 100%;

  .content {
    height: 750px;
    overflow-y: auto;
    padding: 0;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #61396b;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #dfb5e8;
      border-radius: 4px;

      &:hover {
        background: #e8c2f0;
      }
    }

    .content-text {
      position: relative;
      width: 100%;

      .title {
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: auto;
          height: 56px;
        }
      }

      .code {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 25px;
        width: 575px;
        height: 41px;
        margin-bottom: 10px;
        background: no-repeat center left;
        background-size: contain;

        input {
          background: none;
          width: 220px;
          padding: 0 10px;
          color: #fff;
          text-indent: 0;
          font-size: 18px;
          text-align: center;
        }

        &.code1 {
          background-image: url(@/assets/imgs/pop/prize-list/code1.png);
        }

        &.code2 {
          background-image: url(@/assets/imgs/pop/prize-list/code2.png);
        }

        &.code3 {
          background-image: url(@/assets/imgs/pop/prize-list/code3.png);
        }

        &.code4 {
          background-image: url(@/assets/imgs/pop/prize-list/code4.png);
        }

        &.code5 {
          background-image: url(@/assets/imgs/pop/prize-list/code5.png);
        }
      }

      .lottery {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 575px;
        height: 95px;
        background: no-repeat center left;
        background-size: contain;

        &.lottery1 {
          background-image: url(@/assets/imgs/pop/prize-list/lottery1.png);
        }

        &.lottery2 {
          background-image: url(@/assets/imgs/pop/prize-list/lottery2.png);
        }

        &.lottery3 {
          background-image: url(@/assets/imgs/pop/prize-list/lottery3.png);
        }

        &.lottery5 {
          height: 41px;
          background-image: url(@/assets/imgs/pop/prize-list/lottery5.png);
        }
      }

      .btn {
        width: 106px;
        height: 40px;
        cursor: pointer;
        background-size: 100%;

        &.get {
          background-image: url(@/assets/imgs/pop/prize-list/get-btn.png);
        }

        &.lottery {
          background-image: url(@/assets/imgs/pop/prize-list/lottery-btn.png);
        }

        &.copy {
          background-image: url(@/assets/imgs/pop/copy-btn.png);
        }
      }

    }
  }

  .confirm-btn {
    position: absolute;
    bottom: 2%;
    left: 50%;
    transform: translateX(-50%);
    width: 296px;
    height: 69px;
    background: url("@/assets/imgs/pop/rules/btn.png") no-repeat;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateX(-50%) scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.59rem;
    height: 9rem;
    padding: 0.4rem 0.25rem 0 0.4rem;

    .content {
      height: 7.5rem;

      .content-text {
        .title {
          img {
            height: 0.56rem;
          }
        }

        .code {
          width: 4.46rem;
          height: 0.41rem;
          gap: 0.25rem;
          margin-bottom: 0.1rem;

          input {
            width: 2rem;
            padding: 0 0.1rem;
            font-size: 0.18rem;
            margin-right: .15rem;
          }
        }

        .lottery {
          width: 4.36rem;
          height: 0.95rem;

          &.lottery5 {
            height: 0.41rem;
          }
        }

        .btn {
          position: absolute;
          right: 0.2rem;
          width: 1.06rem;
          height: 0.4rem;
        }
      }
    }

    .confirm-btn {
      width: 2.96rem;
      height: 0.69rem;
      background-size: 100%;
    }
  }
}
</style>
