<template>
  <div class="bg">
    <div class="close" @click="emit('close')"></div>
    <div class="content">
      <CountDown class="countdown" />
      <div class="prize-box">
        <div class="prize prize1">
          <img src="@/assets/imgs/pop/order/prize1.png" alt="" />
        </div>
        <div class="prize prize2">
          <img src="@/assets/imgs/pop/order/prize2.png" alt="" />
        </div>
        <div class="prize prize3">
          <img src="@/assets/imgs/pop/order/prize3.png" alt="" />
        </div>
      </div>
      <PhoneInput class="phone-input" :class="{ error: phoneError }" v-model="phone" />
      <div class="privacy-btn" @click="openPrivacyPopup"></div>
      <div class="confirm-btn breathe" @click="submit">
        <img src="@/assets/imgs/pop/order/confirm-btn.png" alt="" />
      </div>
      <div class="store-btns">
        <WebM class="store-btn breathe" :src="WebMGooglePlay" :mov-src="WebMGooglePlayMov" @click="handleStoreClick('gpnew', 'gp')" />
        <WebM class="store-btn breathe" :src="WebMAppleStore" :mov-src="WebMAppleStoreMov" @click="handleStoreClick('iosnew', 'ios')" />
      </div>
    </div>
    <teleport to="body">
      <PrivacyPopup ref="privacyPopRef" />
    </teleport>
  </div>
</template>

<script setup lang="ts">
import CountDown from "../CountDown.vue";
import PhoneInput from "@/components/PhoneInput.vue";
import WebM from "@/components/WebM.vue";
import WebMAppleStore from "@/assets/imgs/webm/apple-store.webm";
import WebMAppleStoreMov from "@/assets/imgs/webm/apple-store.mov";
import WebMGooglePlay from "@/assets/imgs/webm/google-play.webm";
import WebMGooglePlayMov from "@/assets/imgs/webm/google-play.mov";
import { storeToRefs } from "pinia";
import { getUseAppStore, type ToLinkCode } from "@kingnet/kingnet-pre-test";
import { useMediaButton } from "@/hooks/useMediaButton";
import PrivacyPopup from "@/components/popup/PrivacyPopup.vue";
import { ref } from "vue";

const emit = defineEmits<{
  next: [];
  close: [];
}>();

const store = getUseAppStore()();
const { phone, phoneError } = storeToRefs(store);
const { toStore } = useMediaButton();

const privacyPopRef = ref<InstanceType<typeof PrivacyPopup> | null>(null);

const openPrivacyPopup = () => {
  privacyPopRef.value?.open();
};

const handleStoreClick = (eventName: string, code: ToLinkCode) => {
  toStore(eventName, code);
};

const submit = async () => {
  store.ga4TrackEvent("reserve");
  const { success } = await store.toOrder(false, [
    { eventName: "allappointment", deduplicationType: "none" },
    { eventName: "allappointmentonly", deduplicationType: "ip" },
  ]);
  if (success) {
    emit("next");
  }
};
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 653px;
  height: 832px;
  background: url("@/assets/imgs/pop/order/bg.png") no-repeat;
  background-size: 100% 100%;

  .close {
    position: absolute;
    top: -30px;
    right: 30px;
    width: 60px;
    height: 59px;
    background: url("@/assets/imgs/pop/close.png") no-repeat 100%/100%;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #b294bc);
    }
  }

  .content {
    display: flex;
    align-items: center;
    flex-direction: column;

    .countdown {
      margin-top: 70px;
    }

    .prize-box {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
      margin-top: 30px;
    }

    .phone-input {
      width: 460px;
      max-height: 52px;
      margin: 25px auto 0;

      :deep(.phone-input-wrapper) {
        background: #cbcbcb;
      }

      :deep(.area-code) {
        color: #161616;
        font-size: 25px;
      }

      :deep(.phone-input) {
        color: #666;
        font-size: 25px;

        &::placeholder {
          color: #666;
        }
      }

      &.error {
        :deep(.phone-input) {
          color: #ff0000;
        }
      }
    }

    .privacy-btn {
      position: absolute;
      top: 522px;
      right: 130px;
      width: 80px;
      height: 25px;
      cursor: pointer;
    }

    .confirm-btn {
      width: 370px;
      height: 148px;
      margin: 50px auto 0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: drop-shadow(0 0 20px #7364a0);
      }
    }

    .store-btns {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;

      .store-btn {
        width: 240px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          filter: drop-shadow(0 0 20px #7364a0);
        }
      }
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.53rem;
    height: 8.32rem;

    .close {
      top: -0.3rem;
      right: 0.3rem;
      width: 0.6rem;
      height: 0.59rem;
    }

    .content {
      .countdown {
        margin: 0.6rem auto 0;
      }

      .prize-box {
        gap: 0.15rem;
        margin-top: 0.15rem;

        .prize {
          width: 1.58rem;
          height: auto;
        }
      }

      .phone-input {
        width: 4.6rem;
        max-height: 0.52rem;
        margin: 0.15rem auto 0;

        :deep(.area-code) {
          font-size: 0.25rem;
        }

        :deep(.phone-input) {
          font-size: 0.25rem;

          &::placeholder {
            font-size: 0.23rem;
          }
        }

        &.error {
          :deep(.phone-input) {
            color: #ff0000;
          }
        }
      }

      .privacy-btn {
        top: 5.22rem;
        right: 1.3rem;
        width: 0.8rem;
        height: 0.25rem;
      }

      .confirm-btn {
        width: 3.7rem;
        height: 1.48rem;
        margin: 0.4rem auto 0;
      }

      .store-btns {
        margin-top: -0.2rem;

        .store-btn {
          width: 2.4rem;
        }
      }
    }
  }
}
</style>
