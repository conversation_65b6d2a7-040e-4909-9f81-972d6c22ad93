<template>
  <!-- 第2屏到第3屏过渡动画 -->
  <transition name="transition-fade">
    <div v-if="showTransitionAnimation" class="transition-animation-overlay" @click.stop @wheel.stop.prevent>
      <WebM class="intro-petal" :src="WebMPetal" :mov-src="WebMPetalMov" />
      <div class="transition-content">
        <WebM class="intro-title-animation" :src="WebMIntroTitle" :mov-src="WebMIntroTitleMov" />
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref } from "vue";
import WebM from "@/components/WebM.vue";

// WebM 资源导入
import WebMIntroTitle from "@/assets/imgs/webm/intro-title.webm";
import WebMIntroTitleMov from "@/assets/imgs/webm/intro-title.mov";
import WebMPetal from "@/assets/imgs/webm/petal.webm";
import WebMPetalMov from "@/assets/imgs/webm/petal.mov";

// Props 定义
interface Props {
  /** 是否自动播放动画（当从第2屏切换到第3屏时） */
  autoPlay?: boolean;
  /** 动画持续时间（毫秒） */
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  autoPlay: true,
  duration: 2500,
});

// Emits 定义
const emit = defineEmits<{
  /** 动画开始播放 */
  animationStart: [];
  /** 动画播放结束 */
  animationEnd: [];
}>();

// 响应式数据
const showTransitionAnimation = ref(false);
const isAnimationPlaying = ref(false);

// 常量
const ANIMATION_STORAGE_KEY = "p2-to-p3-animation-watched";

// 检查用户是否已观看过动画
const hasWatchedAnimation = (): boolean => {
  return localStorage.getItem(ANIMATION_STORAGE_KEY) === "true";
};

// 标记用户已观看动画
const markAnimationWatched = (): void => {
  localStorage.setItem(ANIMATION_STORAGE_KEY, "true");
};

// 播放过渡动画
const playTransitionAnimation = (): void => {
  if (!props.autoPlay || hasWatchedAnimation()) {
    return;
  }

  showTransitionAnimation.value = true;
  isAnimationPlaying.value = true;
  emit("animationStart");

  // 指定时间后结束动画
  setTimeout(() => {
    showTransitionAnimation.value = false;
    isAnimationPlaying.value = false;
    markAnimationWatched();
    emit("animationEnd");
  }, props.duration);
};

// 手动播放动画（忽略缓存）
const forcePlayAnimation = (): void => {
  showTransitionAnimation.value = true;
  isAnimationPlaying.value = true;
  emit("animationStart");

  setTimeout(() => {
    showTransitionAnimation.value = false;
    isAnimationPlaying.value = false;
    emit("animationEnd");
  }, props.duration);
};

// 暴露方法给父组件
defineExpose({
  playTransitionAnimation,
  forcePlayAnimation,
  isAnimationPlaying: () => isAnimationPlaying.value,
});
</script>

<style lang="less" scoped>
// 第2屏到第3屏过渡动画样式
.transition-animation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-image: url("@/assets/imgs/p3/intro-bg.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .intro-petal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
  }

  .transition-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 过渡动画的fade效果
.transition-fade-enter-active {
  transition: opacity 0.1s ease-in;
}

.transition-fade-leave-active {
  transition: opacity 1.8s ease-out;
}

.transition-fade-enter-from {
  opacity: 0;
}

.transition-fade-leave-to {
  opacity: 0;
}

@media (max-width: 540px) {
  .transition-animation-overlay {
    background-image: url("@/assets/imgs/p3/m/intro-bg.jpg");
  }
}
</style>
