<template>
  <div class="base-swiper-container">
    <swiper :initial-slide="initialSlide" :modules="modules" :direction="direction" :slides-per-view="slidesPerView"
      :space-between="spaceBetween" :speed="speed" :loop="loop" :autoplay="autoplayConfig"
      :pagination="paginationConfig" :navigation="navigationConfig" :mousewheel="mousewheelConfig"
      :keyboard="keyboardConfig" :scrollbar="scrollbarConfig" :free-mode="freeMode" :grab-cursor="grabCursor"
      :centered-slides="centeredSlides" :lazy="lazy" :zoom="zoom" :effect="effect" :coverflow-effect="coverflowEffect"
      :css-mode="cssMode" :virtual="virtual" :breakpoints="breakpoints" :class="['base-swiper', customClass]"
      @swiper="onSwiperInit" @slide-change="onSlideChange" @slide-change-transition-start="onSlideChangeTransitionStart"
      @slide-change-transition-end="onSlideChangeTransitionEnd" @transition-start="onTransitionStart"
      @transition-end="onTransitionEnd" @reach-beginning="onReachBeginning" @reach-end="onReachEnd"
      @from-edge="onFromEdge" @click="onClick" @tap="onTap" @double-tap="onDoubleTap" @slider-move="onSliderMove">
      <slot />
    </swiper>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Swiper } from 'swiper/vue'
import { useSwiper } from '@/hooks/useSwiper'

// 导入 Swiper 样式
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'
import 'swiper/css/free-mode'
import 'swiper/css/zoom'
import 'swiper/css/effect-fade'
import 'swiper/css/effect-cube'
import 'swiper/css/effect-flip'
import 'swiper/css/effect-coverflow'
import 'swiper/css/effect-cards'
import 'swiper/css/effect-creative'

// 定义 Props 接口
interface Props {
  // 基础配置
  initialSlide?: number
  direction?: 'horizontal' | 'vertical'
  slidesPerView?: number | 'auto'
  spaceBetween?: number
  speed?: number
  loop?: boolean

  // 自动播放配置
  autoplay?: boolean | {
    delay?: number
    disableOnInteraction?: boolean
    pauseOnMouseEnter?: boolean
    reverseDirection?: boolean
    stopOnLastSlide?: boolean
    waitForTransition?: boolean
  }

  // 分页配置
  pagination?: boolean | {
    el?: string
    clickable?: boolean
    type?: 'bullets' | 'fraction' | 'progressbar' | 'custom'
    dynamicBullets?: boolean
    dynamicMainBullets?: number
    hideOnClick?: boolean
    renderBullet?: (index: number, className: string) => string
    renderFraction?: (currentClass: string, totalClass: string) => string
    renderProgressbar?: (progressbarFillClass: string) => string
    renderCustom?: (swiper: any, current: number, total: number) => string
  }

  // 导航配置
  navigation?: boolean | {
    nextEl?: string
    prevEl?: string
    hideOnClick?: boolean
    disabledClass?: string
    hiddenClass?: string
    lockClass?: string
  }

  // 鼠标滚轮配置
  mousewheel?: boolean | {
    forceToAxis?: boolean
    releaseOnEdges?: boolean
    invert?: boolean
    thresholdDelta?: number
    thresholdTime?: number
    sensitivity?: number
    eventsTarget?: string | HTMLElement
  }

  // 键盘配置
  keyboard?: boolean | {
    enabled?: boolean
    onlyInViewport?: boolean
    pageUpDown?: boolean
  }

  // 滚动条配置
  scrollbar?: boolean | {
    el?: string
    hide?: boolean
    draggable?: boolean
    snapOnRelease?: boolean
    dragSize?: number | 'auto'
    lockClass?: string
    dragClass?: string
  }

  // 其他配置
  freeMode?: boolean | object
  grabCursor?: boolean
  centeredSlides?: boolean
  lazy?: boolean | object
  zoom?: boolean | object
  effect?: 'slide' | 'fade' | 'cube' | 'coverflow' | 'flip' | 'cards' | 'creative'
  coverflowEffect?: any
  cssMode?: boolean
  virtual?: boolean | object
  breakpoints?: Record<number, any>

  // 自定义样式
  customClass?: string

  // 模块配置
  modules?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'horizontal',
  slidesPerView: 1,
  spaceBetween: 0,
  speed: 300,
  loop: false,
  autoplay: false,
  pagination: false,
  navigation: false,
  mousewheel: false,
  keyboard: false,
  scrollbar: false,
  freeMode: false,
  grabCursor: false,
  centeredSlides: false,
  lazy: false,
  zoom: false,
  effect: 'slide',
  cssMode: false,
  virtual: false,
  customClass: '',
  modules: () => []
})

const emit = defineEmits<{
  swiperReady: [swiper: any]
  slideChange: [swiper: any]
  slideChangeTransitionStart: [swiper: any]
  slideChangeTransitionEnd: [swiper: any]
  transitionStart: [swiper: any]
  transitionEnd: [swiper: any]
  reachBeginning: [swiper: any]
  reachEnd: [swiper: any]
  fromEdge: [swiper: any]
  click: [swiper: any, event: Event]
  tap: [swiper: any, event: Event]
  doubleTap: [swiper: any, event: Event]
  sliderMove: [swiper: any, event: Event]
}>()

// 使用 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  onSwiperInit: handleSwiperInit,
  onSlideChange: handleSlideChange,
  goToSlide,
  slideNext,
  slidePrev,
  startAutoplay,
  stopAutoplay,
  modules
} = useSwiper(props.modules)

// 计算配置属性
const autoplayConfig = computed(() => {
  if (typeof props.autoplay === 'boolean') {
    return props.autoplay
  }
  return props.autoplay
})

const paginationConfig = computed(() => {
  if (typeof props.pagination === 'boolean') {
    return props.pagination ? { clickable: true } : false
  }
  return props.pagination
})

const navigationConfig = computed(() => {
  if (typeof props.navigation === 'boolean') {
    return props.navigation
  }
  return props.navigation
})

const mousewheelConfig = computed(() => {
  if (typeof props.mousewheel === 'boolean') {
    return props.mousewheel
  }
  return props.mousewheel
})

const keyboardConfig = computed(() => {
  if (typeof props.keyboard === 'boolean') {
    return props.keyboard ? { enabled: true } : false
  }
  return props.keyboard
})

const scrollbarConfig = computed(() => {
  if (typeof props.scrollbar === 'boolean') {
    return props.scrollbar ? { draggable: true } : false
  }
  return props.scrollbar
})

// 事件处理函数
const onSwiperInit = (swiper: any) => {
  handleSwiperInit(swiper)
  emit('swiperReady', swiper)
}

const onSlideChange = (swiper: any) => {
  handleSlideChange(swiper)
  emit('slideChange', swiper)
}

const onSlideChangeTransitionStart = (swiper: any) => {
  emit('slideChangeTransitionStart', swiper)
}

const onSlideChangeTransitionEnd = (swiper: any) => {
  emit('slideChangeTransitionEnd', swiper)
}

const onTransitionStart = (swiper: any) => {
  emit('transitionStart', swiper)
}

const onTransitionEnd = (swiper: any) => {
  emit('transitionEnd', swiper)
}

const onReachBeginning = (swiper: any) => {
  emit('reachBeginning', swiper)
}

const onReachEnd = (swiper: any) => {
  emit('reachEnd', swiper)
}

const onFromEdge = (swiper: any) => {
  emit('fromEdge', swiper)
}

const onClick = (swiper: any, event: Event) => {
  emit('click', swiper, event)
}

const onTap = (swiper: any, event: Event) => {
  emit('tap', swiper, event)
}

const onDoubleTap = (swiper: any, event: Event) => {
  emit('doubleTap', swiper, event)
}

const onSliderMove = (swiper: any, event: Event) => {
  emit('sliderMove', swiper, event)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  slideNext,
  slidePrev,
  startAutoplay,
  stopAutoplay
})
</script>

<style lang="less" scoped>
.base-swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.base-swiper {
  width: 100%;
  height: 100%;
}
</style>
