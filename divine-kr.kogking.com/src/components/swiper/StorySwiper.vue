<template>
  <div class="story-swiper-container">
    <BaseSwiper
      :slides-per-view="swiperConfig.slidesPerView"
      :centered-slides="swiperConfig.centeredSlides"
      :speed="swiperConfig.speed"
      :effect="swiperConfig.effect"
      :direction="swiperConfig.direction"
      :coverflow-effect="swiperConfig.coverflowEffect"
      :navigation="swiperConfig.navigation"
      :modules="modules"
      :loop="true"
      :autoplay="{ delay: 5000, disableOnInteraction: false }"
      custom-class="story-swiper"
    >
      <swiper-slide v-for="(story, index) in features" :key="index" class="story-slide">
        <img :src="story.image" />
      </swiper-slide>
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { SwiperSlide } from "swiper/vue";
import BaseSwiper from "./BaseSwiper.vue";
import { useSwiper } from "@/hooks/useSwiper";
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { Navigation, Autoplay, EffectCoverflow } from "swiper/modules";

// 动态导入图片资源
const getFeatureImage = (imageName: string) => {
  return new URL(`../../assets/imgs/p7/${imageName}.jpg`, import.meta.url).href;
};

const emit = defineEmits<{
  slideChange: [index: number];
  swiperReady: [swiper: any];
}>();

// 设备检测
const { isMobile } = useDeviceDetection();

// 使用基础 Swiper Hook
const { modules } = useSwiper([Navigation, Autoplay, EffectCoverflow]);

// Swiper 配置
interface SwiperConfig {
  slidesPerView: number | "auto";
  centeredSlides: boolean;
  speed: number;
  effect: "coverflow";
  direction?: "horizontal" | "vertical";
  coverflowEffect: {
    slideShadows: boolean;
    stretch: string | number;
    depth: number;
    rotate: number;
  };
  navigation: boolean;
}

// 根据设备类型动态配置 Swiper
const swiperConfig = computed<SwiperConfig>(() => {
  if (isMobile.value) {
    // 移动端配置：垂直方向的 coverflow 效果
    return {
      slidesPerView: "auto",
      centeredSlides: true,
      speed: 800,
      effect: "coverflow",
      direction: "vertical",
      coverflowEffect: {
        slideShadows: false,
        stretch: 20, // 移动端使用数值而非百分比
        depth: 200,
        rotate: 0,
      },
      navigation: true,
    };
  } else {
    // PC端配置：水平方向的 coverflow 效果
    return {
      slidesPerView: "auto",
      centeredSlides: true,
      speed: 800,
      effect: "coverflow",
      direction: "horizontal",
      coverflowEffect: {
        slideShadows: false,
        stretch: "20%",
        depth: 200,
        rotate: 0,
      },
      navigation: true,
    };
  }
});

// 动态生成 features 数据
const featureCount = 5;
const features = Array.from({ length: featureCount }, (_, index) => ({
  image: getFeatureImage(`feature${index + 1}`),
}));
</script>

<style lang="less" scoped>
.story-swiper-container {
  position: relative;
  width: 1300px;
  margin-top: 120px; // 为顶部Tab按钮留出空间
  z-index: 2;

  // 移动端适配
  @media (max-width: 540px) {
    width: 94%;
    height: 55vh; // 移动端垂直方向需要固定高度
    margin-top: 2.5rem;
  }
}

.story-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1036px;
  height: 588px;
  background: url(@/assets/imgs/p7/feature-bg.png) no-repeat;
  filter: brightness(0.3);
  transition: all 0.3s ease;

  img {
    width: 97%;
    height: auto;
  }

  &.swiper-slide-active {
    filter: brightness(1);
  }

  // 移动端适配
  @media (max-width: 540px) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 7rem;
    height: 3.97rem;
    background-size: contain;

    img {
      width: 96%;
      height: auto;
      object-fit: cover;
    }
  }
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  width: 83px;
  height: 85px;
  z-index: 50;
  background: url(@/assets/imgs/p7/tab2-arrow.png);
  background-size: 100%;
  transition: all 0.3s ease;

  &:after {
    display: none;
  }

  &:hover {
    background: url(@/assets/imgs/p7/tab2-arrow.png);
    filter: drop-shadow(0 0 10px #bedee6);
  }

  // 移动端垂直方向适配
  @media (max-width: 540px) {
    width: 60px;
    height: 60px;
    background-size: contain;

    &:hover {
      background-size: contain;
    }
  }
}

:deep(.swiper-button-prev) {
  transform: rotate(180deg);
  left: 0;

  // 移动端垂直方向：上箭头
  @media (max-width: 540px) {
    transform: rotate(-90deg) translateY(-50%);
    top: 1.6rem;
    left: 50%;
  }
}

:deep(.swiper-button-next) {
  right: 0;

  // 移动端垂直方向：下箭头
  @media (max-width: 540px) {
    transform: rotate(90deg) translateY(50%);
    bottom: 1.2rem;
    top: auto;
    left: 50%;
  }
}

// 移动端垂直 Swiper 特殊样式
@media (max-width: 540px) {
  :deep(.swiper) {
    height: 100%;
  }

  :deep(.swiper-wrapper) {
    height: 100%;
  }
}
</style>
