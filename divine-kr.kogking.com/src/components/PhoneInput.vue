<template>
  <div class="phone-input-container">
    <div class="phone-input-wrapper">
      <!-- 区号部分 -->
      <span class="area-code">010 <span class="separator">-</span></span>

      <!-- 输入框 -->
      <input ref="inputRef" v-model="inputValue" type="text" :placeholder="placeholder" maxlength="8" class="phone-input" @input="onInput" @blur="onBlur" @focus="onFocus" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

interface Props {
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
}

interface Emits {
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
  (e: "blur"): void;
  (e: "focus"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  placeholder: "을 제외한 숫자만 입력해주세요.",
  disabled: false,
});

const emit = defineEmits<Emits>();

const inputRef = ref<HTMLInputElement>();
const inputValue = ref("");
const isFocused = ref(false);

// 监听父组件传入的值
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue || "";
  },
  { immediate: true }
);

// 输入事件处理
const onInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只允许数字输入
  value = value.replace(/[^\d]/g, "");

  // 限制8位
  if (value.length > 8) {
    value = value.slice(0, 8);
  }

  inputValue.value = value;
  emit("update:modelValue", value);
  emit("change", value);
};

// 失焦事件处理
const onBlur = () => {
  isFocused.value = false;
  emit("blur");
};

// 聚焦事件处理
const onFocus = () => {
  isFocused.value = true;
  emit("focus");
};

// 完整的电话号码
const fullPhoneNumber = computed(() => {
  return inputValue.value ? `010-${inputValue.value}` : "";
});

// 暴露方法给父组件
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  fullPhoneNumber,
});
</script>

<style lang="less" scoped>
.phone-input-container {
  width: 100%;
}

.phone-input-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  padding: 8px 60px;
  font-size: 18px;
  transition: all 0.3s ease;
  position: relative;
}

.area-code {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #82648c;
  white-space: nowrap;
  user-select: none;
  font-size: 20px;

  .separator {
    margin-left: 5px;
    font-size: 16px;
  }
}

.phone-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 18px;
  color: #fff;
  min-width: 0;

  &::placeholder {
    color: #fff;
  }

  &:disabled {
    color: #999;
    cursor: not-allowed;
  }
}

// 响应式设计
@media (max-width: 540px) {
  .phone-input-wrapper {
    padding: 0.08rem 0.4rem;
    border-radius: 0.04rem;
  }

  .phone-input {
    font-size: 0.2rem; // 防止iOS缩放
  }

  .area-code {
    font-size: 0.25rem;
  }
}
</style>
