<template>
  <div class="scan-light-image" :class="{ scanning: isScanning, breathe: breathe }" @click="triggerScan">
    <!-- 图片内容 -->
    <div class="image-container" ref="imageContainerRef">
      <slot>
        <!-- 默认显示，如果没有slot内容则使用src -->
        <img v-if="src" :src="src" :alt="alt" class="default-image" ref="defaultImageRef" />
      </slot>
    </div>

    <!-- 扫光效果层 -->
    <div class="scan-light" :class="[`scan-${scanDirection}`, { active: isScanning }]" :style="scanStyle"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import type { ScanDirection, ScanLightImageProps } from "@/types/ScanLightImage";

// Props 接口定义
interface Props extends ScanLightImageProps {}

const props = withDefaults(defineProps<Props>(), {
  scanDirection: "left-to-right",
  scanDuration: 2,
  autoScan: true,
  autoScanInterval: 1,
  alt: "",
  breathe: true,
});

// 响应式数据
const isScanning = ref(false);
const autoScanTimer = ref<NodeJS.Timeout | null>(null);
const imageUrl = ref<string>("");
const imageContainerRef = ref<HTMLElement>();
const defaultImageRef = ref<HTMLImageElement>();

// 获取图片URL用于mask-image
const getImageUrl = () => {
  if (props.src) {
    imageUrl.value = props.src;
  } else {
    // 如果使用slot，尝试从slot中的img元素获取src
    if (imageContainerRef.value) {
      const imgElement = imageContainerRef.value.querySelector("img") as HTMLImageElement;
      if (imgElement && imgElement.src) {
        imageUrl.value = imgElement.src;
      }
    }
  }
};

// 计算样式

const scanStyle = computed(() => {
  const style: Record<string, string> = {
    animationDuration: `${props.scanDuration}s`,
  };

  // 如果有图片URL，添加mask-image支持
  if (imageUrl.value) {
    style.maskImage = `url(${imageUrl.value})`;
    style.WebkitMaskImage = `url(${imageUrl.value})`;
    style.maskSize = "100%";
    style.maskRepeat = "no-repeat";
    style.maskPosition = "100%";
    style.WebkitMaskSize = "100%";
    style.WebkitMaskRepeat = "no-repeat";
    style.WebkitMaskPosition = "top center";
  }

  return style;
});

// 触发扫光动画
const triggerScan = () => {
  if (isScanning.value) return;

  isScanning.value = true;
  setTimeout(() => {
    isScanning.value = false;
  }, props.scanDuration * 1000);
};

// 设置自动扫光
const setupAutoScan = () => {
  if (autoScanTimer.value) {
    clearInterval(autoScanTimer.value);
  }

  if (props.autoScan) {
    autoScanTimer.value = setInterval(() => {
      triggerScan();
    }, props.autoScanInterval * 1000);
  }
};

// 监听自动扫光配置变化
watch(
  [() => props.autoScan, () => props.autoScanInterval],
  () => {
    setupAutoScan();
  },
  { immediate: true }
);

// 组件挂载时设置自动扫光和获取图片URL
onMounted(() => {
  setupAutoScan();
  // 延迟获取图片URL，确保DOM已渲染
  setTimeout(() => {
    getImageUrl();
  }, 100);

  // 监听图片加载完成
  if (imageContainerRef.value) {
    const imgElement = imageContainerRef.value.querySelector("img") as HTMLImageElement;
    if (imgElement) {
      if (imgElement.complete) {
        getImageUrl();
      } else {
        imgElement.addEventListener("load", getImageUrl);
      }
    }
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoScanTimer.value) {
    clearInterval(autoScanTimer.value);
  }
});

// 暴露方法给父组件
defineExpose({
  triggerScan,
});
</script>

<style lang="less" scoped>
.scan-light-image {
  position: relative;
  display: inline-block;
  cursor: pointer;
  user-select: none;

  .image-container {
    position: relative;
    display: inline-block;

    // 确保图片保持原始尺寸
    img {
      display: block;
      max-width: 100%;
      height: auto;
    }

    .default-image {
      display: block;
      max-width: 100%;
      height: auto;
    }
  }

  .scan-light {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    z-index: 1;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(120deg, transparent, transparent, rgba(255, 255, 255, 0.4), transparent, transparent);
      transform: translateX(-100%);
      // 继承父元素的mask属性
      mask-image: inherit;
      -webkit-mask-image: inherit;
      mask-size: inherit;
      mask-repeat: inherit;
      mask-position: inherit;
      -webkit-mask-size: inherit;
      -webkit-mask-repeat: inherit;
      -webkit-mask-position: inherit;
    }

    // 从左到右扫光
    &.scan-left-to-right {
      &.active {
        opacity: 1;

        &::before {
          animation: scanLeftToRight var(--scan-duration, 2s) ease-out;
        }
      }
    }

    // 从右到左扫光
    &.scan-right-to-left {
      &::before {
        transform: translateX(100%);
      }

      &.active {
        opacity: 1;

        &::before {
          animation: scanRightToLeft var(--scan-duration, 2s) ease-out;
        }
      }
    }

    // 从上到下扫光
    &.scan-top-to-bottom {
      &::before {
        background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 100%);
        transform: translateY(-100%);
      }

      &.active {
        opacity: 1;

        &::before {
          animation: scanTopToBottom var(--scan-duration, 2s) ease-out;
        }
      }
    }

    // 从下到上扫光
    &.scan-bottom-to-top {
      &::before {
        background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.6) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 100%);
        transform: translateY(100%);
      }

      &.active {
        opacity: 1;

        &::before {
          animation: scanBottomToTop var(--scan-duration, 2s) ease-out;
        }
      }
    }
  }
}

// 动画定义
@keyframes scanLeftToRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes scanRightToLeft {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes scanTopToBottom {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes scanBottomToTop {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(-100%);
  }
}
</style>
