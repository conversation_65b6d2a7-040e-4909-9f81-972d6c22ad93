import { ref } from "vue";

export function useCountdown() {
  // 倒计时状态
  const countdownMinute = ref(0);
  const countdownSecond = ref(30);
  const countdownMs = ref(99);
  const countdownTimer = ref<number | null>(null);

  // 格式化倒计时数字
  const formatCountdownNumber = (num: number): string => {
    return num.toString().padStart(2, "0");
  };

  // 开始倒计时
  const startCountdown = () => {
    if (countdownTimer.value) {
      cancelAnimationFrame(countdownTimer.value);
    }

    let lastTime = performance.now();
    let msAccumulator = 0;

    const animate = (currentTime: number) => {
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      // 累积毫秒数
      msAccumulator += deltaTime;

      // 每10毫秒更新一次显示
      if (msAccumulator >= 10) {
        const steps = Math.floor(msAccumulator / 10);
        msAccumulator %= 10;

        // 更新毫秒显示
        countdownMs.value -= steps;

        // 毫秒归零时，秒减1，毫秒重置为99
        while (countdownMs.value < 0) {
          countdownMs.value += 100;
          countdownSecond.value--;

          // 秒归零时，分减1，秒重置为59
          if (countdownSecond.value < 0) {
            countdownSecond.value = 59;
            countdownMinute.value--;

            // 如果分钟也归零，重置整个倒计时
            if (countdownMinute.value < 0) {
              resetCountdown();
            }
          }
        }
      }

      countdownTimer.value = requestAnimationFrame(animate);
    };

    countdownTimer.value = requestAnimationFrame(animate);
  };

  // 停止倒计时
  const stopCountdown = () => {
    if (countdownTimer.value) {
      cancelAnimationFrame(countdownTimer.value);
      countdownTimer.value = null;
    }
  };

  // 重置倒计时
  const resetCountdown = () => {
    countdownMinute.value = 0;
    countdownSecond.value = 30;
    countdownMs.value = 99;
  };

  return {
    // 状态
    countdownMinute,
    countdownSecond,
    countdownMs,

    // 方法
    startCountdown,
    stopCountdown,
    resetCountdown,
    formatCountdownNumber,
  };
}
