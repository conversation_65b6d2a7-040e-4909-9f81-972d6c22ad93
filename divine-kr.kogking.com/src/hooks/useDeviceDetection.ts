import { ref } from "vue";

export function useDeviceDetection() {
  const isIOS = ref(false);
  const isAndroid = ref(false);
  const isMobile = ref(false);
  const isSafari = ref(false);
  const deviceType = ref<"ios" | "android" | "other">("other");

  const detectDevice = () => {
    const userAgent = navigator.userAgent.toLowerCase();

    // 检测Safari浏览器
    const isSafariBrowser = /safari/.test(userAgent) && !/chrome/.test(userAgent);
    isSafari.value = isSafariBrowser;

    // 检测iOS设备
    const iosDevices = /iphone|ipad|ipod/i;
    const isIOSDevice = iosDevices.test(userAgent);

    // 检测Android设备
    const androidDevices = /android/i;
    const isAndroidDevice = androidDevices.test(userAgent);

    // 检测移动设备（包括iOS、Android和其他移动设备）
    const mobileDevices = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;
    const isMobileDevice = mobileDevices.test(userAgent) || window.innerWidth <= 540;

    // 如果是Safari浏览器，也将其识别为iOS设备
    const isIOSOrSafari = isIOSDevice || isSafariBrowser;

    isIOS.value = isIOSOrSafari;
    isAndroid.value = isAndroidDevice;
    isMobile.value = isMobileDevice;

    if (isIOSOrSafari) {
      deviceType.value = "ios";
    } else if (isAndroidDevice) {
      deviceType.value = "android";
    } else {
      deviceType.value = "other";
    }
  };

  // 监听窗口大小变化，动态检测移动端
  const handleResize = () => {
    const wasMobile = isMobile.value;
    detectDevice();
    // 如果移动端状态发生变化，可以触发重新渲染
    if (wasMobile !== isMobile.value) {
      console.log("设备类型变化:", isMobile.value ? "移动端" : "PC端");
    }
  };

  // 初始化时检测设备
  detectDevice();

  // 监听窗口大小变化
  window.addEventListener("resize", handleResize);

  return {
    isIOS,
    isAndroid,
    isMobile,
    isSafari,
    deviceType,
    detectDevice,
  };
}
