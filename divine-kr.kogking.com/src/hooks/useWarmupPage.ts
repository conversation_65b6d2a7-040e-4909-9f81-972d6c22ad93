import { type InitConfig, AreaCode, init, type PopupConfig } from "@kingnet/kingnet-pre-test";
import { platformMap } from "@/config/platformConfig";
import { prizeConfig } from "@/config/prizeConfig";
import { markRaw, onMounted } from "vue";
import OrderPopup from "@/components/popup/OrderPopup.vue";
import OrderAfterPopup from "@/components/popup/OrderAfterPopup.vue";
import OrderStorePopup from "@/components/popup/OrderStorePopup.vue";
import OrderCommunityPopup from "@/components/popup/OrderCommunityPopup.vue";
import { useRoute } from "vue-router";

export function useWarmupPage(popupConfig?: PopupConfig[]) {
  const route = useRoute();

  const defaultPopupConfig = [
    {
      type: "order",
      // 预约弹窗：store内部自动判断是否显示
      children: [
        {
          type: "order",
          weight: 1000,
          showCloseTime: 0,
          delayTime: 10,
          allowOverlayClose: false, // 预约弹窗不允许点击遮罩关闭
          eventName: {
            show: "order_popup_show",
            close: "order_popup_close",
          },
          components: markRaw(OrderPopup), // 精美版预约弹窗
          rewards: [prizeConfig.zykr_order_pet.id],
        },
      ],
    },
    {
      type: "store",
      // 商店弹窗：store内部自动判断是否显示
      children: [
        {
          type: "order_after",
          weight: 1000,
          showCloseTime: 0,
          delayTime: 0,
          allowOverlayClose: false,
          eventName: {
            show: "order_after_popup_show",
            close: "order_after_popup_close",
          },
          components: markRaw(OrderAfterPopup),
          rewards: [prizeConfig.zykr_store_xq.id],
        },
      ],
    },
    {
      type: "storeOrderAfter",
      // 商店下载后弹窗：store内部自动判断是否显示
      children: [
        {
          type: "store_after",
          weight: 1000,
          showCloseTime: 0,
          delayTime: 0,
          allowOverlayClose: false,
          eventName: {
            show: "store_after_popup_show",
            close: "store_after_popup_close",
          },
          components: markRaw(OrderStorePopup),
          rewards: [prizeConfig.zykr_community_growup.id],
        },
      ],
    },
    {
      type: "community",
      // 社群弹窗：store内部自动判断是否显示
      children: [
        {
          type: "community_main",
          weight: 1000,
          showCloseTime: 0,
          delayTime: 0,
          allowOverlayClose: false,
          eventName: {
            show: "community_popup_show",
            close: "community_popup_close",
          },
          components: markRaw(OrderCommunityPopup),
          rewards: [],
        },
      ],
    },
  ];

  const initializeStore = async () => {
    const warmupConfig: InitConfig = {
      baseUrl: "https://website.gamehaza.com",
      platformMap,
      resize: {
        //设计稿尺寸，主要用于计算rem单位，1rem=100px, 第一个参数是最大尺寸，第二个参数是设计稿宽度
        mobile: [540, 750],
        pc: [480, 750],
      },
      businessConfig: {
        en_name: "ZYKR",
        // lottery_active_unique_id: "",
        iosUrl: "https://apps.apple.com/kr/app/id6744669416",
        gpUrl: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
        protocolUrl: "https://webstatis.kogking.com/mythwarskr_protocal.html",
        privacyUrl: "https://webstatis.kogking.com/mythwarskr_privacy.html",
        fbAppId: "1196617402042125",
        requireAgreement: true,
        shareBaseUrl: "https://divine-kr.kogking.com",
        language: "KR",
        communityUrl: "https://game.naver.com/lounge/Divine_Keeper_of_the_Gods/home",
      },
      popupConfig: popupConfig || defaultPopupConfig,
      route,
      // alertComponent: AlertPopup, // 传入通用弹窗组件
      alertConfig: {
        title: "알림",
        confirmText: "확정",
      },
      areaCodes: [AreaCode.KOREA], // 传入需要的区号
      messages: {
        alreadyOrdered: "예약되었습니다！",
        phoneError: "정확한 휴대폰 번호를 입력해 주세요",
        copySuccess: "복사됨",
      }, // 自定义提示语
    };
    await init(warmupConfig);
  };

  onMounted(() => {
    initializeStore();
  });

  // 返回初始化Promise，让调用者知道何时完成
  return Promise.resolve();
}
