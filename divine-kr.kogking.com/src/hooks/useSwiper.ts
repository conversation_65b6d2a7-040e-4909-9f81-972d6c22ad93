import { ref, onUnmounted, type Ref } from "vue";
import type { Swiper as SwiperType } from "swiper";
import { Navigation, Pagination, Mousewheel, Keyboard, Autoplay, Scrollbar, FreeMode, Zoom, EffectFade, EffectCube, EffectFlip, EffectCoverflow, EffectCards, EffectCreative } from "swiper/modules";

// 所有可用的 Swiper 模块
const ALL_MODULES = [Navigation, Pagination, Mousewheel, Keyboard, Autoplay, Scrollbar, FreeMode, Zoom, EffectFade, EffectCube, EffectFlip, EffectCoverflow, EffectCards, EffectCreative];

// 通用 Swiper Hook
export function useSwiper(customModules?: any[]) {
  const swiperInstance = ref<SwiperType | null>(null);
  const currentSlide = ref(0);
  const isReady = ref(false);

  // 确定要使用的模块
  const modules = customModules || ALL_MODULES;

  // 初始化 Swiper 实例
  const onSwiperInit = (swiper: SwiperType) => {
    swiperInstance.value = swiper;
    isReady.value = true;
    currentSlide.value = swiper.activeIndex;
  };

  // 滑动变化回调
  const onSlideChange = (swiper: SwiperType) => {
    currentSlide.value = swiper.activeIndex;
  };

  // 导航到指定滑块
  const goToSlide = (index: number, speed?: number) => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slideTo(index, speed);
    }
  };

  // 下一张
  const slideNext = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slideNext();
    }
  };

  // 上一张
  const slidePrev = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.slidePrev();
    }
  };

  // 开始自动播放
  const startAutoplay = () => {
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.start();
    }
  };

  // 停止自动播放
  const stopAutoplay = () => {
    if (swiperInstance.value?.autoplay) {
      swiperInstance.value.autoplay.stop();
    }
  };

  // 更新 Swiper
  const updateSwiper = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.update();
    }
  };

  // 重新计算尺寸
  const updateSize = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.updateSize();
    }
  };

  // 重新计算滑块
  const updateSlides = () => {
    if (swiperInstance.value && isReady.value) {
      swiperInstance.value.updateSlides();
    }
  };

  // 销毁实例
  const destroySwiper = () => {
    if (swiperInstance.value) {
      swiperInstance.value.destroy();
      swiperInstance.value = null;
      isReady.value = false;
    }
  };

  // 组件卸载时清理
  onUnmounted(() => {
    destroySwiper();
  });

  return {
    swiperInstance: swiperInstance as Ref<SwiperType | null>,
    currentSlide,
    isReady,
    modules,
    onSwiperInit,
    onSlideChange,
    goToSlide,
    slideNext,
    slidePrev,
    startAutoplay,
    stopAutoplay,
    updateSwiper,
    updateSize,
    updateSlides,
    destroySwiper,
  };
}
