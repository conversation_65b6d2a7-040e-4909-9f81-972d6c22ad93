# GitLab私有仓库配置
# 告诉npm从GitLab私有仓库下载@kingnet域的包
@kingnet:registry=https://gitlab.ops.kingnet.com/api/v4/projects/2755/packages/npm/

# 认证token配置（需要替换为实际的项目ID和Deploy Token）
//gitlab.ops.kingnet.com/api/v4/projects/2755/packages/npm/:_authToken=cQgZrtaxXAmssKUSzVys

# 默认npm公共仓库配置（可选，npm会自动回退）
registry=https://registry.npmjs.org/

# 其他常用配置
# 设置npm安装时的超时时间
timeout=60000

# 设置npm缓存目录（可选）
# cache=/path/to/cache

# 设置npm日志级别（可选）
loglevel=warn