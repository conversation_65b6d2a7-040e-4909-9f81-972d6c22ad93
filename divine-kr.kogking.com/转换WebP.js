import fs from "fs";
import path from "path";
import sharp from "sharp";
import { program } from "commander";

// 设置命令行参数
program
  .option("-d, --directory <path>", "要处理的目录路径", "./src/assets/test/")
  .option("-q, --quality <number>", "有损模式的质量 (1-100)", "80")
  .option("-l, --lossless", "使用无损模式", false)
  .option("-r, --replace", "替换原始文件", false)
  .option("-e, --extensions <list>", "要处理的文件扩展名列表", "jpg,jpeg,png")
  .parse(process.argv);

const options = program.opts();

// 目标文件夹路径
const folderPath = options.directory;
// 质量设置 (仅在有损模式下使用)
const quality = parseInt(options.quality, 10);
// 是否使用无损模式
const lossless = options.lossless;
// 是否替换原始文件
const replaceOriginal = options.replace;
// 要处理的文件扩展名
const extensions = options.extensions
  .split(",")
  .map((ext) => ext.toLowerCase());

console.log(`开始转换图片为WebP格式...`);
console.log(`目标目录: ${folderPath}`);
console.log(`模式: ${lossless ? "无损" : "有损 (质量: " + quality + ")"}`);
console.log(`处理文件类型: ${extensions.join(", ")}`);
console.log(`替换原始文件: ${replaceOriginal ? "是" : "否"}`);

// 转换图片为WebP的函数
async function convertToWebP(filePath) {
  try {
    const extension = path.extname(filePath).toLowerCase();
    const fileNameWithoutExt = path.basename(filePath, extension);
    const dirName = path.dirname(filePath);
    const outputPath = replaceOriginal
      ? filePath + ".tmp"
      : path.join(dirName, fileNameWithoutExt + ".webp");

    // 检查文件扩展名是否在处理列表中
    if (!extensions.includes(extension.substring(1))) {
      return;
    }

    // 创建Sharp实例
    const image = sharp(filePath);

    // 根据模式设置WebP选项
    const webpOptions = lossless
      ? { lossless: true }
      : { quality: quality, lossless: false };

    // 转换为WebP
    await image.webp(webpOptions).toFile(outputPath);

    // 如果选择替换原始文件
    if (replaceOriginal) {
      fs.unlinkSync(filePath); // 删除原始文件
      fs.renameSync(outputPath, filePath.replace(extension, ".webp")); // 重命名临时文件
      console.log(
        `转换并替换完成: ${filePath} -> ${filePath.replace(extension, ".webp")}`
      );
    } else {
      console.log(`转换完成: ${filePath} -> ${outputPath}`);
    }
  } catch (error) {
    console.error(`转换失败: ${filePath}`, error);
  }
}

// 递归遍历文件夹并转换图片
function convertImagesInFolder(folder) {
  fs.readdir(folder, (err, files) => {
    if (err) {
      return console.error("无法读取文件夹:", err);
    }

    files.forEach((file) => {
      const filePath = path.join(folder, file);

      // 检查是否是文件夹
      if (fs.statSync(filePath).isDirectory()) {
        // 递归处理子文件夹
        convertImagesInFolder(filePath);
      } else {
        // 处理图片文件
        const extension = path.extname(file).toLowerCase().substring(1);
        if (extensions.includes(extension)) {
          convertToWebP(filePath);
        }
      }
    });
  });
}

// 开始转换
convertImagesInFolder(folderPath);

console.log("转换任务已启动，请等待完成...");
