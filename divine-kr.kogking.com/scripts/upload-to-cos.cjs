const COS = require("cos-nodejs-sdk-v5");
const fs = require("fs");
const path = require("path");

// COS配置 - 请替换为您的实际配置
const config = {
  SecretId: "AKID00oecT7ZoqnX6SjneHh1A7ze6OU8g6IJ",
  SecretKey: "wgEYfaNzOaaOFuNfkcHp9NQL7mFizKUT",
  Bucket: "overseas-static-1254024138", // 格式: bucket-appid
  Region: "ap-singapore",
  KeyPrefix: "divine-kr.kogking.com/dist/",
  DistPath: "../dist",
};

// 初始化COS实例
const cos = new COS({
  SecretId: config.SecretId,
  SecretKey: config.SecretKey,
  FileParallelLimit: 20, // 并发上传数量，建议5-20之间
  ChunkParallelLimit: 8, // 分块上传并发数
  ChunkRetryTimes: 3, // 分块上传失败重试次数
});

// 获取所有需要上传的文件
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    // 跳过.DS_Store和其他隐藏文件
    if (file.startsWith(".")) {
      return;
    }

    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

// 获取文件的MIME类型
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    ".html": "text/html",
    ".css": "text/css",
    ".js": "application/javascript",
    ".json": "application/json",
    ".png": "image/png",
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".gif": "image/gif",
    ".svg": "image/svg+xml",
    ".ico": "image/x-icon",
    ".woff": "font/woff",
    ".woff2": "font/woff2",
    ".ttf": "font/ttf",
    ".eot": "application/vnd.ms-fontobject",
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  };
  return mimeTypes[ext] || "application/octet-stream";
}

// 上传文件到COS（带Content-Type）
function uploadFileWithMime(localPath, cosKey) {
  return new Promise((resolve, reject) => {
    const contentType = getMimeType(localPath);

    cos.putObject(
      {
        Bucket: config.Bucket,
        Region: config.Region,
        Key: cosKey,
        Body: fs.createReadStream(localPath),
        ContentLength: fs.statSync(localPath).size,
        ContentType: contentType,
        CacheControl: localPath.endsWith("index.html") ? "no-cache" : "max-age=31536000",
      },
      (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      }
    );
  });
}

// 主上传函数
async function uploadToCOS() {
  const startTime = Date.now(); // 记录开始时间

  try {
    console.log("🚀 开始上传到腾讯云COS...");

    // 检查配置
    if (!config.SecretId || !config.SecretKey) {
      console.error("❌ 请先配置COS密钥！");
      process.exit(1);
    }

    // 检查Bucket格式
    if (!config.Bucket || !config.Bucket.includes("-")) {
      console.error("❌ COS_BUCKET 格式不正确！");
      console.log("正确格式: your-bucket-name-1250000000");
      console.log("其中 1250000000 是您的腾讯云APPID");
      process.exit(1);
    }

    const distPath = path.join(__dirname, config.DistPath);

    // 检查dist目录是否存在
    if (!fs.existsSync(distPath)) {
      console.error("❌ dist目录不存在，请先执行构建命令");
      process.exit(1);
    }

    // 获取所有文件
    const allFiles = getAllFiles(distPath);

    // 分离index.html和其他文件
    const indexHtmlFiles = allFiles.filter((file) => path.basename(file) === "index.html");
    const otherFiles = allFiles.filter((file) => path.basename(file) !== "index.html");

    console.log(`📁 找到 ${allFiles.length} 个文件需要上传`);
    console.log(`📄 其中 ${otherFiles.length} 个非index.html文件，${indexHtmlFiles.length} 个index.html文件`);

    // 先上传其他文件（并发上传）
    console.log("\n📤 开始上传其他资源文件...");
    let uploadedCount = 0;
    let failedCount = 0;
    const failedFiles = []; // 记录失败的文件

    // 使用Promise.allSettled进行并发上传，但限制并发数
    const concurrency = 5; // 同时上传的文件数量
    const uploadPromises = [];

    for (let i = 0; i < otherFiles.length; i += concurrency) {
      const batch = otherFiles.slice(i, i + concurrency);
      const batchPromises = batch.map(async (file) => {
        const relativePath = path.relative(distPath, file);
        const cosKey = config.KeyPrefix + relativePath.replace(/\\/g, "/");

        try {
          await uploadFileWithMime(file, cosKey);
          uploadedCount++;
          console.log(`✅ [${uploadedCount}/${otherFiles.length}] ${relativePath}`);
          return { success: true, file: relativePath };
        } catch (error) {
          failedCount++;
          console.error(`❌ 上传失败 ${relativePath}:`, error.message);
          failedFiles.push({ file: relativePath, error: error.message });
          return { success: false, file: relativePath, error: error.message };
        }
      });

      await Promise.allSettled(batchPromises);
    }

    // 最后上传index.html文件
    console.log("\n📤 开始上传index.html文件...");
    for (const file of indexHtmlFiles) {
      const relativePath = path.relative(distPath, file);
      const cosKey = config.KeyPrefix + relativePath.replace(/\\/g, "/");

      try {
        await uploadFileWithMime(file, cosKey);
        console.log(`✅ ${relativePath}`);
      } catch (error) {
        failedCount++;
        console.error(`❌ 上传失败 ${relativePath}:`, error.message);
        failedFiles.push({ file: relativePath, error: error.message });
      }
    }

    // 计算上传耗时
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000; // 转换为秒
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);

    console.log("\n🎉 上传完成！");
    console.log(`📊 总计 ${allFiles.length} 个文件，成功 ${uploadedCount + indexHtmlFiles.length - failedCount} 个，失败 ${failedCount} 个`);
    console.log(`⏱️  上传耗时: ${minutes > 0 ? `${minutes}分${seconds}秒` : `${seconds}秒`} (${duration.toFixed(2)}s)`);

    if (allFiles.length > 0) {
      const avgSpeed = (allFiles.length / duration).toFixed(2);
      console.log(`🚀 平均速度: ${avgSpeed} 文件/秒`);
    }

    // 显示失败的文件列表
    if (failedFiles.length > 0) {
      console.log("\n❌ 上传失败的文件:");
      failedFiles.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.file}`);
        console.log(`      错误: ${item.error}`);
      });
      console.log(`\n💡 建议: 检查网络连接和COS配置，然后重新运行上传`);
    }

    const baseUrl = config.KeyPrefix.startsWith("http") ? config.KeyPrefix : `https://${config.Bucket}.cos.${config.Region}.myqcloud.com/${config.KeyPrefix}`;
    console.log(`🌐 访问地址: ${baseUrl}`);
  } catch (error) {
    console.error("❌ 上传过程中发生错误:", error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  uploadToCOS();
}

module.exports = { uploadToCOS };
