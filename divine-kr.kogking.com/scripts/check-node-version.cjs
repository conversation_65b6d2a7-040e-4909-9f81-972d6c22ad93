#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 读取 .nvmrc 文件
const nvmrcPath = path.join(__dirname, '../.nvmrc')
const packageJsonPath = path.join(__dirname, '../package.json')

function checkNodeVersion() {
  try {
    // 获取当前 Node.js 版本
    const currentVersion = process.version.replace('v', '')
    console.log(`🔍 当前 Node.js 版本: v${currentVersion}`)

    // 读取推荐版本
    let recommendedVersion = ''
    if (fs.existsSync(nvmrcPath)) {
      recommendedVersion = fs.readFileSync(nvmrcPath, 'utf8').trim()
      console.log(`📋 推荐 Node.js 版本: v${recommendedVersion}`)
    }

    // 读取 package.json 中的 engines 要求
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      if (packageJson.engines && packageJson.engines.node) {
        console.log(`⚙️  项目要求 Node.js 版本: ${packageJson.engines.node}`)
      }
    }

    // 版本比较
    if (recommendedVersion && currentVersion !== recommendedVersion) {
      console.log('\n⚠️  版本不匹配警告:')
      console.log(`   当前版本: v${currentVersion}`)
      console.log(`   推荐版本: v${recommendedVersion}`)
      console.log('\n💡 建议操作:')
      console.log('   1. 安装 nvm: https://github.com/nvm-sh/nvm')
      console.log(`   2. 切换版本: nvm use ${recommendedVersion}`)
      console.log(`   3. 或安装版本: nvm install ${recommendedVersion}`)
      console.log('\n🚨 不同的 Node.js 版本可能导致构建结果不一致！')
      
      // 在 CI 环境中，版本不匹配应该失败
      if (process.env.CI) {
        console.error('\n❌ CI 环境中检测到 Node.js 版本不匹配，构建终止！')
        process.exit(1)
      }
    } else if (recommendedVersion) {
      console.log('\n✅ Node.js 版本匹配！')
    }

    // 检查 pnpm 版本
    try {
      const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim()
      console.log(`📦 当前 pnpm 版本: v${pnpmVersion}`)
    } catch (error) {
      console.log('\n⚠️  未检测到 pnpm，建议安装:')
      console.log('   npm install -g pnpm')
    }

  } catch (error) {
    console.error('❌ 版本检查失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('🔧 Node.js 版本检查工具\n')
  checkNodeVersion()
}

module.exports = { checkNodeVersion }