import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  let base = "/";
  if (command === "build") {
    base = "https://static-kr.kyxy777.com/divine-kr.kogking.com/dist/";
  }
  return {
    plugins: [vue(), vueJsx()],
    base,
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
        components: path.resolve(__dirname, "src/components"),
        // 添加更多别名
      },
    },
    server: {
      host: true, // 或者设置为 '0.0.0.0'
      proxy: {
        "/website": {
          target: "https://website.gamehaza.com", // 代理目标地址
          changeOrigin: true, // 是否更改原始主机头为目标URL
          rewrite: (path) => path.replace(/^\/api/, ""), // 重写路径
        },
        "/api/official": {
          target: "https://divine-kr.kogking.com", // 代理目标地址
          changeOrigin: true, // 解决跨域问题
          rewrite: (path) => path.replace(/^\/api\/official/, "/official"), // 重写路径
        },
      },
    },
    css: {
      postcss: {
        plugins: [],
      },
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          // 其他 less 配置
        },
      },
    },
  };
});
